import React, { createContext, type PropsWithChildren, useContext } from 'react';
import classNames from 'clsx';
import { Progress } from 'radix-ui';
import Badge from '../Badge';
import { type BadgeProps, SHAPE, SIZE, THEME } from '../Badge/Badge.types';

const ProgressBarActiveColorMap = {
  primary: {
    default: 'bg-brand',
    disabled: 'bg-indigo-300',
  },
  success: {
    default: 'bg-green-500',
    disabled: 'bg-green-300',
  },
  warning: {
    default: 'bg-yellow-500',
    disabled: 'bg-yellow-300',
  },
  danger: {
    default: 'bg-red-500',
    disabled: 'bg-red-300',
  },
  secondary: {
    default: 'bg-gray-500',
    disabled: 'bg-gray-300',
  },
};

const ProgressBarStrokeColorMap = {
  primary: 'stroke-brand',
  success: 'stroke-success-bold',
  warning: 'stroke-warning-bold',
  danger: 'stroke-danger-bold',
  secondary: 'stroke-neutral-bold'
};

const ProgressBarSizeMap = {
  small: 'h-1',
  medium: 'h-2',
  large: 'h-3',
};

const DonutSizeMap = {
  small: {
    size: 24,
    strokeWidth: 3,
    radius: 10,
    fontSize: 'text-sm',
    showProgress: false,
  },
  medium: {
    size: 40,
    strokeWidth: 4,
    radius: 16,
    fontSize: 'text-sm',
    showProgress: true,
  },
  large: {
    size: 64,
    strokeWidth: 6,
    radius: 24,
    fontSize: 'text-sm',
    showProgress: true,
  },
};

export type ProgressBarActiveColor = 'primary' | 'success' | 'warning' | 'danger' | 'secondary';

export type ProgressBarSize = 'small' | 'medium' | 'large';

export type ProgressBarState = 'default' | 'disabled';

export type ProgressBarVariant = 'linear' | 'donut';

export type ProgressBarProps = {
  color?: ProgressBarActiveColor;
  progress: number;
  size?: ProgressBarSize;
  disabled?: boolean;
  variant?: ProgressBarVariant;
};

type ProgressBarContextValue = {
  progress: number;
  size: ProgressBarSize;
  variant: ProgressBarVariant;
};

const ProgressBarContext = createContext<ProgressBarContextValue | null>(null);

export const useProgressBarContext = () => {
  const context = useContext(ProgressBarContext);
  if (!context) {
    throw new Error('ProgressBar child components must be used within a ProgressBar.Root component');
  }
  return context;
};

const DonutProgress: React.FC<ProgressBarProps> = ({ color = 'primary', progress, size = 'medium', disabled }) => {
  const config = DonutSizeMap[size];
  const { size: svgSize, strokeWidth, radius, fontSize, showProgress } = config;

  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;
  const strokeColorClass = ProgressBarStrokeColorMap[color];
  return (
    <div className="relative inline-flex items-center justify-center">
      <svg width={svgSize} height={svgSize} viewBox={`0 0 ${svgSize} ${svgSize}`} className="transform -rotate-90">
        <circle
          cx={svgSize / 2}
          cy={svgSize / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="none"
          className='stroke-neutral-subtle'
        />
        <circle
          cx={svgSize / 2}
          cy={svgSize / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={classNames(
            'transition-all duration-300 ease-in-out',
            strokeColorClass
          )}
        />
      </svg>
      {showProgress && (
        <div
          className={classNames(
            'absolute inset-0 flex items-center justify-center',
            fontSize,
            'text-xs leading-4 font-medium text-neutral-subtle'
          )}
        >
          {progress}%
        </div>
      )}
    </div>
  );
};

export const ProgressBarRoot = React.forwardRef<
  React.ElementRef<typeof Progress.Root>,
  React.ComponentPropsWithoutRef<typeof Progress.Root> & ProgressBarProps
>(
  (
    {
      color = 'primary',
      progress,
      size = 'medium',
      className,
      children,
      disabled,
      variant = 'linear',
      ...progressBarProps
    },
    ref
  ) => {
    const contextValue: ProgressBarContextValue = {
      progress,
      size,
      variant,
    };

    if (variant === 'donut') {
      return (
        <ProgressBarContext.Provider value={contextValue}>
          <div className="flex flex-col items-center space-y-2">
            <DonutProgress color={color} progress={progress} size={size} disabled={disabled} />
            {children}
          </div>
        </ProgressBarContext.Provider>
      );
    }

    return (
      <ProgressBarContext.Provider value={contextValue}>
        <div className="flex flex-col space-y-1">
          {children}

          <Progress.Root
            ref={ref}
            value={progress}
            className={classNames(
              'w-full rounded-full bg-neutral-subtle relative',
              ProgressBarSizeMap[size],
              className
            )}
            aria-disabled={disabled}
            {...(progressBarProps as any)}
          >
            <Progress.Indicator
              aria-disabled={disabled}
              className={classNames(
                'rounded-full bg-brand',
                ProgressBarSizeMap[size],
                ProgressBarActiveColorMap[color][(disabled ? 'disabled' : 'default') as ProgressBarState]
              )}
              style={{ width: `${progress}%` }}
            />
          </Progress.Root>
        </div>
      </ProgressBarContext.Provider>
    );
  }
);
ProgressBarRoot.displayName = 'ProgressBar.Root';

export interface ProgressBarHeaderProps {
  showProgress?: boolean;
}

export const ProgressBarHeader: React.FC<PropsWithChildren<ProgressBarHeaderProps>> = ({ children, showProgress }) => {
  const { progress, size, variant } = useProgressBarContext();

  if (variant === 'donut') {
    return <div className="flex flex-col items-center space-y-1">{children}</div>;
  }

  return (
    <div className="flex justify-between space-x-2 items-baseline">
      {children && <div className="flex flex-col">{children}</div>}
      {showProgress && (
        <span
          className={classNames('text-gray-500 dark:text-gray-300 leading-4 font-medium ml-auto', {
            'text-[10px]': size === 'small',
            'text-xs': size === 'medium' || size === 'large',
          })}
        >
          {progress}%
        </span>
      )}
    </div>
  );
};
ProgressBarHeader.displayName = 'ProgressBar.Header';

export const ProgressBarTitle: React.FC<PropsWithChildren> = ({ children }) => (
  <span className="text-neutral dark:text-neutral-inverse text-sm leading-5 font-medium flex items-center gap-x-1">
    {children}
  </span>
);
ProgressBarTitle.displayName = 'ProgressBar.Title';

export const ProgressBarSubtitle: React.FC<PropsWithChildren> = ({ children }) => (
  <span className="text-neutral-subtle dark:text-neutral-subtlest text-sm leading-5 font-medium flex items-center gap-x-1">
    {children}
  </span>
);
ProgressBarSubtitle.displayName = 'ProgressBar.Subtitle';

export { THEME as ProgressBarBadgeTheme };
export const ProgressBarBadge: React.FC<PropsWithChildren<Pick<BadgeProps, 'theme' | 'label'>>> = ({
  label,
  theme,
}) => <Badge label={label} size={SIZE.EXTRA_SMALL} shape={SHAPE.BASIC} theme={theme} />;
ProgressBarBadge.displayName = 'ProgressBar.Badge';
