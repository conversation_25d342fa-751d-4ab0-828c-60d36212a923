@import "tailwindcss";

@theme {
  --background-color-accent-blue: var(--color-background-accent-blue-default);
  --background-color-accent-blue-hovered: var(--color-background-accent-blue-hovered);
  --background-color-accent-blue-pressed: var(--color-background-accent-blue-pressed);
  --background-color-accent-blue-alpha: var(--color-background-accent-blue-alpha-default);
  --background-color-accent-blue-alpha-hovered: var(--color-background-accent-blue-alpha-hovered);
  --background-color-accent-blue-alpha-pressed: var(--color-background-accent-blue-alpha-pressed);
  --background-color-accent-blue-bold: var(--color-background-accent-blue-bold-default);
  --background-color-accent-blue-bold-hovered: var(--color-background-accent-blue-bold-hovered);
  --background-color-accent-blue-bold-pressed: var(--color-background-accent-blue-bold-pressed);
  --background-color-accent-blue-bolder: var(--color-background-accent-blue-bolder-default);
  --background-color-accent-blue-bolder-hovered: var(--color-background-accent-blue-bolder-hovered);
  --background-color-accent-blue-bolder-pressed: var(--color-background-accent-blue-bolder-pressed);
  --background-color-accent-blue-subtle: var(--color-background-accent-blue-subtle-default);
  --background-color-accent-blue-subtle-hovered: var(--color-background-accent-blue-subtle-hovered);
  --background-color-accent-blue-subtle-pressed: var(--color-background-accent-blue-subtle-pressed);
  --background-color-accent-blue-subtlest: var(--color-background-accent-blue-subtlest-default);
  --background-color-accent-blue-subtlest-hovered: var(--color-background-accent-blue-subtlest-hovered);
  --background-color-accent-blue-subtlest-pressed: var(--color-background-accent-blue-subtlest-pressed);
  --background-color-accent-blue-white: var(--color-background-accent-blue-white-default);
  --background-color-accent-blue-white-hovered: var(--color-background-accent-blue-white-hovered);
  --background-color-accent-blue-white-pressed: var(--color-background-accent-blue-white-pressed);
  --background-color-accent-cyan: var(--color-background-accent-cyan-default);
  --background-color-accent-cyan-hovered: var(--color-background-accent-cyan-hovered);
  --background-color-accent-cyan-pressed: var(--color-background-accent-cyan-pressed);
  --background-color-accent-cyan-alpha: var(--color-background-accent-cyan-alpha-default);
  --background-color-accent-cyan-alpha-hovered: var(--color-background-accent-cyan-alpha-hovered);
  --background-color-accent-cyan-alpha-pressed: var(--color-background-accent-cyan-alpha-pressed);
  --background-color-accent-cyan-bold: var(--color-background-accent-cyan-bold-default);
  --background-color-accent-cyan-bold-hovered: var(--color-background-accent-cyan-bold-hovered);
  --background-color-accent-cyan-bold-pressed: var(--color-background-accent-cyan-bold-pressed);
  --background-color-accent-cyan-bolder: var(--color-background-accent-cyan-bolder-default);
  --background-color-accent-cyan-bolder-hovered: var(--color-background-accent-cyan-bolder-hovered);
  --background-color-accent-cyan-bolder-pressed: var(--color-background-accent-cyan-bolder-pressed);
  --background-color-accent-cyan-subtle: var(--color-background-accent-cyan-subtle-default);
  --background-color-accent-cyan-subtle-hovered: var(--color-background-accent-cyan-subtle-hovered);
  --background-color-accent-cyan-subtle-pressed: var(--color-background-accent-cyan-subtle-pressed);
  --background-color-accent-cyan-subtlest: var(--color-background-accent-cyan-subtlest-default);
  --background-color-accent-cyan-subtlest-hovered: var(--color-background-accent-cyan-subtlest-hovered);
  --background-color-accent-cyan-subtlest-pressed: var(--color-background-accent-cyan-subtlest-pressed);
  --background-color-accent-cyan-white: var(--color-background-accent-cyan-white-default);
  --background-color-accent-cyan-white-hovered: var(--color-background-accent-cyan-white-hovered);
  --background-color-accent-cyan-white-pressed: var(--color-background-accent-cyan-white-pressed);
  --background-color-accent-emerald: var(--color-background-accent-emerald-default);
  --background-color-accent-emerald-hovered: var(--color-background-accent-emerald-hovered);
  --background-color-accent-emerald-pressed: var(--color-background-accent-emerald-pressed);
  --background-color-accent-emerald-alpha: var(--color-background-accent-emerald-alpha-default);
  --background-color-accent-emerald-alpha-hovered: var(--color-background-accent-emerald-alpha-hovered);
  --background-color-accent-emerald-alpha-pressed: var(--color-background-accent-emerald-alpha-pressed);
  --background-color-accent-emerald-bold: var(--color-background-accent-emerald-bold-default);
  --background-color-accent-emerald-bold-hovered: var(--color-background-accent-emerald-bold-hovered);
  --background-color-accent-emerald-bold-pressed: var(--color-background-accent-emerald-bold-pressed);
  --background-color-accent-emerald-bolder: var(--color-background-accent-emerald-bolder-default);
  --background-color-accent-emerald-bolder-hovered: var(--color-background-accent-emerald-bolder-hovered);
  --background-color-accent-emerald-bolder-pressed: var(--color-background-accent-emerald-bolder-pressed);
  --background-color-accent-emerald-subtle: var(--color-background-accent-emerald-subtle-default);
  --background-color-accent-emerald-subtle-hovered: var(--color-background-accent-emerald-subtle-hovered);
  --background-color-accent-emerald-subtle-pressed: var(--color-background-accent-emerald-subtle-pressed);
  --background-color-accent-emerald-subtlest: var(--color-background-accent-emerald-subtlest-default);
  --background-color-accent-emerald-subtlest-hovered: var(--color-background-accent-emerald-subtlest-hovered);
  --background-color-accent-emerald-subtlest-pressed: var(--color-background-accent-emerald-subtlest-pressed);
  --background-color-accent-emerald-white: var(--color-background-accent-emerald-white-default);
  --background-color-accent-emerald-white-hovered: var(--color-background-accent-emerald-white-hovered);
  --background-color-accent-emerald-white-pressed: var(--color-background-accent-emerald-white-pressed);
  --background-color-accent-fuchsia: var(--color-background-accent-fuchsia-default);
  --background-color-accent-fuchsia-hovered: var(--color-background-accent-fuchsia-hovered);
  --background-color-accent-fuchsia-pressed: var(--color-background-accent-fuchsia-pressed);
  --background-color-accent-fuchsia-alpha: var(--color-background-accent-fuchsia-alpha-default);
  --background-color-accent-fuchsia-alpha-hovered: var(--color-background-accent-fuchsia-alpha-hovered);
  --background-color-accent-fuchsia-alpha-pressed: var(--color-background-accent-fuchsia-alpha-pressed);
  --background-color-accent-fuchsia-bold: var(--color-background-accent-fuchsia-bold-default);
  --background-color-accent-fuchsia-bold-hovered: var(--color-background-accent-fuchsia-bold-hovered);
  --background-color-accent-fuchsia-bold-pressed: var(--color-background-accent-fuchsia-bold-pressed);
  --background-color-accent-fuchsia-bolder: var(--color-background-accent-fuchsia-bolder-default);
  --background-color-accent-fuchsia-bolder-hovered: var(--color-background-accent-fuchsia-bolder-hovered);
  --background-color-accent-fuchsia-bolder-pressed: var(--color-background-accent-fuchsia-bolder-pressed);
  --background-color-accent-fuchsia-subtle: var(--color-background-accent-fuchsia-subtle-default);
  --background-color-accent-fuchsia-subtle-hovered: var(--color-background-accent-fuchsia-subtle-hovered);
  --background-color-accent-fuchsia-subtle-pressed: var(--color-background-accent-fuchsia-subtle-pressed);
  --background-color-accent-fuchsia-subtlest: var(--color-background-accent-fuchsia-subtlest-default);
  --background-color-accent-fuchsia-subtlest-hovered: var(--color-background-accent-fuchsia-subtlest-hovered);
  --background-color-accent-fuchsia-subtlest-pressed: var(--color-background-accent-fuchsia-subtlest-pressed);
  --background-color-accent-fuchsia-white: var(--color-background-accent-fuchsia-white-default);
  --background-color-accent-fuchsia-white-hovered: var(--color-background-accent-fuchsia-white-hovered);
  --background-color-accent-fuchsia-white-pressed: var(--color-background-accent-fuchsia-white-pressed);
  --background-color-accent-green: var(--color-background-accent-green-default);
  --background-color-accent-green-hovered: var(--color-background-accent-green-hovered);
  --background-color-accent-green-pressed: var(--color-background-accent-green-pressed);
  --background-color-accent-green-alpha: var(--color-background-accent-green-alpha-default);
  --background-color-accent-green-alpha-hovered: var(--color-background-accent-green-alpha-hovered);
  --background-color-accent-green-alpha-pressed: var(--color-background-accent-green-alpha-pressed);
  --background-color-accent-green-bold: var(--color-background-accent-green-bold-default);
  --background-color-accent-green-bold-hovered: var(--color-background-accent-green-bold-hovered);
  --background-color-accent-green-bold-pressed: var(--color-background-accent-green-bold-pressed);
  --background-color-accent-green-bolder: var(--color-background-accent-green-bolder-default);
  --background-color-accent-green-bolder-hovered: var(--color-background-accent-green-bolder-hovered);
  --background-color-accent-green-bolder-pressed: var(--color-background-accent-green-bolder-pressed);
  --background-color-accent-green-subtle: var(--color-background-accent-green-subtle-default);
  --background-color-accent-green-subtle-hovered: var(--color-background-accent-green-subtle-hovered);
  --background-color-accent-green-subtle-pressed: var(--color-background-accent-green-subtle-pressed);
  --background-color-accent-green-subtlest: var(--color-background-accent-green-subtlest-default);
  --background-color-accent-green-subtlest-hovered: var(--color-background-accent-green-subtlest-hovered);
  --background-color-accent-green-subtlest-pressed: var(--color-background-accent-green-subtlest-pressed);
  --background-color-accent-green-white: var(--color-background-accent-green-white-default);
  --background-color-accent-green-white-hovered: var(--color-background-accent-green-white-hovered);
  --background-color-accent-green-white-pressed: var(--color-background-accent-green-white-pressed);
  --background-color-accent-indigo: var(--color-background-accent-indigo-default);
  --background-color-accent-indigo-hovered: var(--color-background-accent-indigo-hovered);
  --background-color-accent-indigo-pressed: var(--color-background-accent-indigo-pressed);
  --background-color-accent-indigo-alpha: var(--color-background-accent-indigo-alpha-default);
  --background-color-accent-indigo-alpha-hovered: var(--color-background-accent-indigo-alpha-hovered);
  --background-color-accent-indigo-alpha-pressed: var(--color-background-accent-indigo-alpha-pressed);
  --background-color-accent-indigo-bold: var(--color-background-accent-indigo-bold-default);
  --background-color-accent-indigo-bold-hovered: var(--color-background-accent-indigo-bold-hovered);
  --background-color-accent-indigo-bold-pressed: var(--color-background-accent-indigo-bold-pressed);
  --background-color-accent-indigo-bolder: var(--color-background-accent-indigo-bolder-default);
  --background-color-accent-indigo-bolder-hovered: var(--color-background-accent-indigo-bolder-hovered);
  --background-color-accent-indigo-bolder-pressed: var(--color-background-accent-indigo-bolder-pressed);
  --background-color-accent-indigo-subtle: var(--color-background-accent-indigo-subtle-default);
  --background-color-accent-indigo-subtle-hovered: var(--color-background-accent-indigo-subtle-hovered);
  --background-color-accent-indigo-subtle-pressed: var(--color-background-accent-indigo-subtle-pressed);
  --background-color-accent-indigo-subtlest: var(--color-background-accent-indigo-subtlest-default);
  --background-color-accent-indigo-subtlest-hovered: var(--color-background-accent-indigo-subtlest-hovered);
  --background-color-accent-indigo-subtlest-pressed: var(--color-background-accent-indigo-subtlest-pressed);
  --background-color-accent-indigo-white: var(--color-background-accent-indigo-white-default);
  --background-color-accent-indigo-white-hovered: var(--color-background-accent-indigo-white-hovered);
  --background-color-accent-indigo-white-pressed: var(--color-background-accent-indigo-white-pressed);
  --background-color-accent-lime: var(--color-background-accent-lime-default);
  --background-color-accent-lime-hovered: var(--color-background-accent-lime-hovered);
  --background-color-accent-lime-pressed: var(--color-background-accent-lime-pressed);
  --background-color-accent-lime-alpha: var(--color-background-accent-lime-alpha-default);
  --background-color-accent-lime-alpha-hovered: var(--color-background-accent-lime-alpha-hovered);
  --background-color-accent-lime-alpha-pressed: var(--color-background-accent-lime-alpha-pressed);
  --background-color-accent-lime-bold: var(--color-background-accent-lime-bold-default);
  --background-color-accent-lime-bold-hovered: var(--color-background-accent-lime-bold-hovered);
  --background-color-accent-lime-bold-pressed: var(--color-background-accent-lime-bold-pressed);
  --background-color-accent-lime-bolder: var(--color-background-accent-lime-bolder-default);
  --background-color-accent-lime-bolder-hovered: var(--color-background-accent-lime-bolder-hovered);
  --background-color-accent-lime-bolder-pressed: var(--color-background-accent-lime-bolder-pressed);
  --background-color-accent-lime-subtle: var(--color-background-accent-lime-subtle-default);
  --background-color-accent-lime-subtle-hovered: var(--color-background-accent-lime-subtle-hovered);
  --background-color-accent-lime-subtle-pressed: var(--color-background-accent-lime-subtle-pressed);
  --background-color-accent-lime-subtlest: var(--color-background-accent-lime-subtlest-default);
  --background-color-accent-lime-subtlest-hovered: var(--color-background-accent-lime-subtlest-hovered);
  --background-color-accent-lime-subtlest-pressed: var(--color-background-accent-lime-subtlest-pressed);
  --background-color-accent-lime-white: var(--color-background-accent-lime-white-default);
  --background-color-accent-lime-white-hovered: var(--color-background-accent-lime-white-hovered);
  --background-color-accent-lime-white-pressed: var(--color-background-accent-lime-white-pressed);
  --background-color-accent-orange: var(--color-background-accent-orange-default);
  --background-color-accent-orange-hovered: var(--color-background-accent-orange-hovered);
  --background-color-accent-orange-pressed: var(--color-background-accent-orange-pressed);
  --background-color-accent-orange-alpha: var(--color-background-accent-orange-alpha-default);
  --background-color-accent-orange-alpha-hovered: var(--color-background-accent-orange-alpha-hovered);
  --background-color-accent-orange-alpha-pressed: var(--color-background-accent-orange-alpha-pressed);
  --background-color-accent-orange-bold: var(--color-background-accent-orange-bold-default);
  --background-color-accent-orange-bold-hovered: var(--color-background-accent-orange-bold-hovered);
  --background-color-accent-orange-bold-pressed: var(--color-background-accent-orange-bold-pressed);
  --background-color-accent-orange-bolder: var(--color-background-accent-orange-bolder-default);
  --background-color-accent-orange-bolder-hovered: var(--color-background-accent-orange-bolder-hovered);
  --background-color-accent-orange-bolder-pressed: var(--color-background-accent-orange-bolder-pressed);
  --background-color-accent-orange-subtle: var(--color-background-accent-orange-subtle-default);
  --background-color-accent-orange-subtle-hovered: var(--color-background-accent-orange-subtle-hovered);
  --background-color-accent-orange-subtle-pressed: var(--color-background-accent-orange-subtle-pressed);
  --background-color-accent-orange-subtlest: var(--color-background-accent-orange-subtlest-default);
  --background-color-accent-orange-subtlest-hovered: var(--color-background-accent-orange-subtlest-hovered);
  --background-color-accent-orange-subtlest-pressed: var(--color-background-accent-orange-subtlest-pressed);
  --background-color-accent-orange-white: var(--color-background-accent-orange-white-default);
  --background-color-accent-orange-white-hovered: var(--color-background-accent-orange-white-hovered);
  --background-color-accent-orange-white-pressed: var(--color-background-accent-orange-white-pressed);
  --background-color-accent-pink: var(--color-background-accent-pink-default);
  --background-color-accent-pink-hovered: var(--color-background-accent-pink-hovered);
  --background-color-accent-pink-pressed: var(--color-background-accent-pink-pressed);
  --background-color-accent-pink-alpha: var(--color-background-accent-pink-alpha-default);
  --background-color-accent-pink-alpha-hovered: var(--color-background-accent-pink-alpha-hovered);
  --background-color-accent-pink-alpha-pressed: var(--color-background-accent-pink-alpha-pressed);
  --background-color-accent-pink-bold: var(--color-background-accent-pink-bold-default);
  --background-color-accent-pink-bold-hovered: var(--color-background-accent-pink-bold-hovered);
  --background-color-accent-pink-bold-pressed: var(--color-background-accent-pink-bold-pressed);
  --background-color-accent-pink-bolder: var(--color-background-accent-pink-bolder-default);
  --background-color-accent-pink-bolder-hovered: var(--color-background-accent-pink-bolder-hovered);
  --background-color-accent-pink-bolder-pressed: var(--color-background-accent-pink-bolder-pressed);
  --background-color-accent-pink-subtle: var(--color-background-accent-pink-subtle-default);
  --background-color-accent-pink-subtle-hovered: var(--color-background-accent-pink-subtle-hovered);
  --background-color-accent-pink-subtle-pressed: var(--color-background-accent-pink-subtle-pressed);
  --background-color-accent-pink-subtlest: var(--color-background-accent-pink-subtlest-default);
  --background-color-accent-pink-subtlest-hovered: var(--color-background-accent-pink-subtlest-hovered);
  --background-color-accent-pink-subtlest-pressed: var(--color-background-accent-pink-subtlest-pressed);
  --background-color-accent-pink-white: var(--color-background-accent-pink-white-default);
  --background-color-accent-pink-white-hovered: var(--color-background-accent-pink-white-hovered);
  --background-color-accent-pink-white-pressed: var(--color-background-accent-pink-white-pressed);
  --background-color-accent-purple: var(--color-background-accent-purple-default);
  --background-color-accent-purple-hovered: var(--color-background-accent-purple-hovered);
  --background-color-accent-purple-pressed: var(--color-background-accent-purple-pressed);
  --background-color-accent-purple-alpha: var(--color-background-accent-purple-alpha-default);
  --background-color-accent-purple-alpha-hovered: var(--color-background-accent-purple-alpha-hovered);
  --background-color-accent-purple-alpha-pressed: var(--color-background-accent-purple-alpha-pressed);
  --background-color-accent-purple-bold: var(--color-background-accent-purple-bold-default);
  --background-color-accent-purple-bold-hovered: var(--color-background-accent-purple-bold-hovered);
  --background-color-accent-purple-bold-pressed: var(--color-background-accent-purple-bold-pressed);
  --background-color-accent-purple-bolder: var(--color-background-accent-purple-bolder-default);
  --background-color-accent-purple-bolder-hovered: var(--color-background-accent-purple-bolder-hovered);
  --background-color-accent-purple-bolder-pressed: var(--color-background-accent-purple-bolder-pressed);
  --background-color-accent-purple-subtle: var(--color-background-accent-purple-subtle-default);
  --background-color-accent-purple-subtle-hovered: var(--color-background-accent-purple-subtle-hovered);
  --background-color-accent-purple-subtle-pressed: var(--color-background-accent-purple-subtle-pressed);
  --background-color-accent-purple-subtlest: var(--color-background-accent-purple-subtlest-default);
  --background-color-accent-purple-subtlest-hovered: var(--color-background-accent-purple-subtlest-hovered);
  --background-color-accent-purple-subtlest-pressed: var(--color-background-accent-purple-subtlest-pressed);
  --background-color-accent-purple-white: var(--color-background-accent-purple-white-default);
  --background-color-accent-purple-white-hovered: var(--color-background-accent-purple-white-hovered);
  --background-color-accent-purple-white-pressed: var(--color-background-accent-purple-white-pressed);
  --background-color-accent-red: var(--color-background-accent-red-default);
  --background-color-accent-red-hovered: var(--color-background-accent-red-hovered);
  --background-color-accent-red-pressed: var(--color-background-accent-red-pressed);
  --background-color-accent-red-alpha: var(--color-background-accent-red-alpha-default);
  --background-color-accent-red-alpha-hovered: var(--color-background-accent-red-alpha-hovered);
  --background-color-accent-red-alpha-pressed: var(--color-background-accent-red-alpha-pressed);
  --background-color-accent-red-bold: var(--color-background-accent-red-bold-default);
  --background-color-accent-red-bold-hovered: var(--color-background-accent-red-bold-hovered);
  --background-color-accent-red-bold-pressed: var(--color-background-accent-red-bold-pressed);
  --background-color-accent-red-bolder: var(--color-background-accent-red-bolder-default);
  --background-color-accent-red-bolder-hovered: var(--color-background-accent-red-bolder-hovered);
  --background-color-accent-red-bolder-pressed: var(--color-background-accent-red-bolder-pressed);
  --background-color-accent-red-subtle: var(--color-background-accent-red-subtle-default);
  --background-color-accent-red-subtle-hovered: var(--color-background-accent-red-subtle-hovered);
  --background-color-accent-red-subtle-pressed: var(--color-background-accent-red-subtle-pressed);
  --background-color-accent-red-subtlest: var(--color-background-accent-red-subtlest-default);
  --background-color-accent-red-subtlest-hovered: var(--color-background-accent-red-subtlest-hovered);
  --background-color-accent-red-subtlest-pressed: var(--color-background-accent-red-subtlest-pressed);
  --background-color-accent-red-white: var(--color-background-accent-red-white-default);
  --background-color-accent-red-white-hovered: var(--color-background-accent-red-white-hovered);
  --background-color-accent-red-white-pressed: var(--color-background-accent-red-white-pressed);
  --background-color-accent-rose: var(--color-background-accent-rose-default);
  --background-color-accent-rose-hovered: var(--color-background-accent-rose-hovered);
  --background-color-accent-rose-pressed: var(--color-background-accent-rose-pressed);
  --background-color-accent-rose-alpha: var(--color-background-accent-rose-alpha-default);
  --background-color-accent-rose-alpha-hovered: var(--color-background-accent-rose-alpha-hovered);
  --background-color-accent-rose-alpha-pressed: var(--color-background-accent-rose-alpha-pressed);
  --background-color-accent-rose-bold: var(--color-background-accent-rose-bold-default);
  --background-color-accent-rose-bold-hovered: var(--color-background-accent-rose-bold-hovered);
  --background-color-accent-rose-bold-pressed: var(--color-background-accent-rose-bold-pressed);
  --background-color-accent-rose-bolder: var(--color-background-accent-rose-bolder-default);
  --background-color-accent-rose-bolder-hovered: var(--color-background-accent-rose-bolder-hovered);
  --background-color-accent-rose-bolder-pressed: var(--color-background-accent-rose-bolder-pressed);
  --background-color-accent-rose-subtle: var(--color-background-accent-rose-subtle-default);
  --background-color-accent-rose-subtle-hovered: var(--color-background-accent-rose-subtle-hovered);
  --background-color-accent-rose-subtle-pressed: var(--color-background-accent-rose-subtle-pressed);
  --background-color-accent-rose-subtlest: var(--color-background-accent-rose-subtlest-default);
  --background-color-accent-rose-subtlest-hovered: var(--color-background-accent-rose-subtlest-hovered);
  --background-color-accent-rose-subtlest-pressed: var(--color-background-accent-rose-subtlest-pressed);
  --background-color-accent-rose-white: var(--color-background-accent-rose-white-default);
  --background-color-accent-rose-white-hovered: var(--color-background-accent-rose-white-hovered);
  --background-color-accent-rose-white-pressed: var(--color-background-accent-rose-white-pressed);
  --background-color-accent-rosedust: var(--color-background-accent-rosedust-default);
  --background-color-accent-rosedust-hovered: var(--color-background-accent-rosedust-hovered);
  --background-color-accent-rosedust-pressed: var(--color-background-accent-rosedust-pressed);
  --background-color-accent-rosedust-alpha: var(--color-background-accent-rosedust-alpha-default);
  --background-color-accent-rosedust-alpha-hovered: var(--color-background-accent-rosedust-alpha-hovered);
  --background-color-accent-rosedust-alpha-pressed: var(--color-background-accent-rosedust-alpha-pressed);
  --background-color-accent-rosedust-bold: var(--color-background-accent-rosedust-bold-default);
  --background-color-accent-rosedust-bold-hovered: var(--color-background-accent-rosedust-bold-hovered);
  --background-color-accent-rosedust-bold-pressed: var(--color-background-accent-rosedust-bold-pressed);
  --background-color-accent-rosedust-bolder: var(--color-background-accent-rosedust-bolder-default);
  --background-color-accent-rosedust-bolder-hovered: var(--color-background-accent-rosedust-bolder-hovered);
  --background-color-accent-rosedust-bolder-pressed: var(--color-background-accent-rosedust-bolder-pressed);
  --background-color-accent-rosedust-subtle: var(--color-background-accent-rosedust-subtle-default);
  --background-color-accent-rosedust-subtle-hovered: var(--color-background-accent-rosedust-subtle-hovered);
  --background-color-accent-rosedust-subtle-pressed: var(--color-background-accent-rosedust-subtle-pressed);
  --background-color-accent-rosedust-subtlest: var(--color-background-accent-rosedust-subtlest-default);
  --background-color-accent-rosedust-subtlest-hovered: var(--color-background-accent-rosedust-subtlest-hovered);
  --background-color-accent-rosedust-subtlest-pressed: var(--color-background-accent-rosedust-subtlest-pressed);
  --background-color-accent-rosedust-white: var(--color-background-accent-rosedust-white-default);
  --background-color-accent-rosedust-white-hovered: var(--color-background-accent-rosedust-white-hovered);
  --background-color-accent-rosedust-white-pressed: var(--color-background-accent-rosedust-white-pressed);
  --background-color-accent-sky: var(--color-background-accent-sky-default);
  --background-color-accent-sky-hovered: var(--color-background-accent-sky-hovered);
  --background-color-accent-sky-pressed: var(--color-background-accent-sky-pressed);
  --background-color-accent-sky-alpha: var(--color-background-accent-sky-alpha-default);
  --background-color-accent-sky-alpha-hovered: var(--color-background-accent-sky-alpha-hovered);
  --background-color-accent-sky-alpha-pressed: var(--color-background-accent-sky-alpha-pressed);
  --background-color-accent-sky-bold: var(--color-background-accent-sky-bold-default);
  --background-color-accent-sky-bold-hovered: var(--color-background-accent-sky-bold-hovered);
  --background-color-accent-sky-bold-pressed: var(--color-background-accent-sky-bold-pressed);
  --background-color-accent-sky-bolder: var(--color-background-accent-sky-bolder-default);
  --background-color-accent-sky-bolder-hovered: var(--color-background-accent-sky-bolder-hovered);
  --background-color-accent-sky-bolder-pressed: var(--color-background-accent-sky-bolder-pressed);
  --background-color-accent-sky-subtle: var(--color-background-accent-sky-subtle-default);
  --background-color-accent-sky-subtle-hovered: var(--color-background-accent-sky-subtle-hovered);
  --background-color-accent-sky-subtle-pressed: var(--color-background-accent-sky-subtle-pressed);
  --background-color-accent-sky-subtlest: var(--color-background-accent-sky-subtlest-default);
  --background-color-accent-sky-subtlest-hovered: var(--color-background-accent-sky-subtlest-hovered);
  --background-color-accent-sky-subtlest-pressed: var(--color-background-accent-sky-subtlest-pressed);
  --background-color-accent-sky-white: var(--color-background-accent-sky-white-default);
  --background-color-accent-sky-white-hovered: var(--color-background-accent-sky-white-hovered);
  --background-color-accent-sky-white-pressed: var(--color-background-accent-sky-white-pressed);
  --background-color-accent-teal: var(--color-background-accent-teal-default);
  --background-color-accent-teal-hovered: var(--color-background-accent-teal-hovered);
  --background-color-accent-teal-pressed: var(--color-background-accent-teal-pressed);
  --background-color-accent-teal-alpha: var(--color-background-accent-teal-alpha-default);
  --background-color-accent-teal-alpha-hovered: var(--color-background-accent-teal-alpha-hovered);
  --background-color-accent-teal-alpha-pressed: var(--color-background-accent-teal-alpha-pressed);
  --background-color-accent-teal-bold: var(--color-background-accent-teal-bold-default);
  --background-color-accent-teal-bold-hovered: var(--color-background-accent-teal-bold-hovered);
  --background-color-accent-teal-bold-pressed: var(--color-background-accent-teal-bold-pressed);
  --background-color-accent-teal-bolder: var(--color-background-accent-teal-bolder-default);
  --background-color-accent-teal-bolder-hovered: var(--color-background-accent-teal-bolder-hovered);
  --background-color-accent-teal-bolder-pressed: var(--color-background-accent-teal-bolder-pressed);
  --background-color-accent-teal-subtle: var(--color-background-accent-teal-subtle-default);
  --background-color-accent-teal-subtle-hovered: var(--color-background-accent-teal-subtle-hovered);
  --background-color-accent-teal-subtle-pressed: var(--color-background-accent-teal-subtle-pressed);
  --background-color-accent-teal-subtlest: var(--color-background-accent-teal-subtlest-default);
  --background-color-accent-teal-subtlest-hovered: var(--color-background-accent-teal-subtlest-hovered);
  --background-color-accent-teal-subtlest-pressed: var(--color-background-accent-teal-subtlest-pressed);
  --background-color-accent-teal-white: var(--color-background-accent-teal-white-default);
  --background-color-accent-teal-white-hovered: var(--color-background-accent-teal-white-hovered);
  --background-color-accent-teal-white-pressed: var(--color-background-accent-teal-white-pressed);
  --background-color-accent-yellow: var(--color-background-accent-yellow-default);
  --background-color-accent-yellow-hovered: var(--color-background-accent-yellow-hovered);
  --background-color-accent-yellow-pressed: var(--color-background-accent-yellow-pressed);
  --background-color-accent-yellow-alpha: var(--color-background-accent-yellow-alpha-default);
  --background-color-accent-yellow-alpha-hovered: var(--color-background-accent-yellow-alpha-hovered);
  --background-color-accent-yellow-alpha-pressed: var(--color-background-accent-yellow-alpha-pressed);
  --background-color-accent-yellow-bold: var(--color-background-accent-yellow-bold-default);
  --background-color-accent-yellow-bold-hovered: var(--color-background-accent-yellow-bold-hovered);
  --background-color-accent-yellow-bold-pressed: var(--color-background-accent-yellow-bold-pressed);
  --background-color-accent-yellow-bolder: var(--color-background-accent-yellow-bolder-default);
  --background-color-accent-yellow-bolder-hovered: var(--color-background-accent-yellow-bolder-hovered);
  --background-color-accent-yellow-bolder-pressed: var(--color-background-accent-yellow-bolder-pressed);
  --background-color-accent-yellow-subtle: var(--color-background-accent-yellow-subtle-default);
  --background-color-accent-yellow-subtle-hovered: var(--color-background-accent-yellow-subtle-hovered);
  --background-color-accent-yellow-subtle-pressed: var(--color-background-accent-yellow-subtle-pressed);
  --background-color-accent-yellow-subtlest: var(--color-background-accent-yellow-subtlest-default);
  --background-color-accent-yellow-subtlest-hovered: var(--color-background-accent-yellow-subtlest-hovered);
  --background-color-accent-yellow-subtlest-pressed: var(--color-background-accent-yellow-subtlest-pressed);
  --background-color-accent-yellow-white: var(--color-background-accent-yellow-white-default);
  --background-color-accent-yellow-white-hovered: var(--color-background-accent-yellow-white-hovered);
  --background-color-accent-yellow-white-pressed: var(--color-background-accent-yellow-white-pressed);
  --background-color-brand: var(--color-background-brand-default);
  --background-color-brand-hovered: var(--color-background-brand-hovered);
  --background-color-brand-pressed: var(--color-background-brand-pressed);
  --background-color-brand-alpha-bold: var(--color-background-brand-alpha-bold-default);
  --background-color-brand-alpha-bold-hovered: var(--color-background-brand-alpha-bold-hovered);
  --background-color-brand-alpha-bold-pressed: var(--color-background-brand-alpha-bold-pressed);
  --background-color-brand-alpha-subtle: var(--color-background-brand-alpha-subtle-default);
  --background-color-brand-alpha-subtle-hovered: var(--color-background-brand-alpha-subtle-hovered);
  --background-color-brand-alpha-subtle-pressed: var(--color-background-brand-alpha-subtle-pressed);
  --background-color-brand-bold: var(--color-background-brand-bold-default);
  --background-color-brand-bold-hovered: var(--color-background-brand-bold-hovered);
  --background-color-brand-bold-pressed: var(--color-background-brand-bold-pressed);
  --background-color-brand-bolder: var(--color-background-brand-bolder-default);
  --background-color-brand-bolder-hovered: var(--color-background-brand-bolder-hovered);
  --background-color-brand-bolder-pressed: var(--color-background-brand-bolder-pressed);
  --background-color-brand-subtle: var(--color-background-brand-subtle-default);
  --background-color-brand-subtle-hovered: var(--color-background-brand-subtle-hovered);
  --background-color-brand-subtle-pressed: var(--color-background-brand-subtle-pressed);
  --background-color-brand-subtlest: var(--color-background-brand-subtlest-default);
  --background-color-brand-subtlest-hovered: var(--color-background-brand-subtlest-hovered);
  --background-color-brand-subtlest-pressed: var(--color-background-brand-subtlest-pressed);
  --background-color-brand-white: var(--color-background-brand-white-default);
  --background-color-brand-white-hovered: var(--color-background-brand-white-hovered);
  --background-color-brand-white-pressed: var(--color-background-brand-white-pressed);
  --background-color-danger: var(--color-background-danger-default);
  --background-color-danger-hovered: var(--color-background-danger-hovered);
  --background-color-danger-pressed: var(--color-background-danger-pressed);
  --background-color-danger-alpha-bold: var(--color-background-danger-alpha-bold-default);
  --background-color-danger-alpha-bold-hovered: var(--color-background-danger-alpha-bold-hovered);
  --background-color-danger-alpha-bold-pressed: var(--color-background-danger-alpha-bold-pressed);
  --background-color-danger-alpha-subtle: var(--color-background-danger-alpha-subtle-default);
  --background-color-danger-alpha-subtle-hovered: var(--color-background-danger-alpha-subtle-hovered);
  --background-color-danger-alpha-subtle-pressed: var(--color-background-danger-alpha-subtle-pressed);
  --background-color-danger-bold: var(--color-background-danger-bold-default);
  --background-color-danger-bold-hovered: var(--color-background-danger-bold-hovered);
  --background-color-danger-bold-pressed: var(--color-background-danger-bold-pressed);
  --background-color-danger-bolder: var(--color-background-danger-bolder-default);
  --background-color-danger-bolder-hovered: var(--color-background-danger-bolder-hovered);
  --background-color-danger-bolder-pressed: var(--color-background-danger-bolder-pressed);
  --background-color-danger-subtle: var(--color-background-danger-subtle-default);
  --background-color-danger-subtle-hovered: var(--color-background-danger-subtle-hovered);
  --background-color-danger-subtle-pressed: var(--color-background-danger-subtle-pressed);
  --background-color-danger-subtlest: var(--color-background-danger-subtlest-default);
  --background-color-danger-subtlest-hovered: var(--color-background-danger-subtlest-hovered);
  --background-color-danger-subtlest-pressed: var(--color-background-danger-subtlest-pressed);
  --background-color-danger-white: var(--color-background-danger-white-default);
  --background-color-danger-white-hovered: var(--color-background-danger-white-hovered);
  --background-color-danger-white-pressed: var(--color-background-danger-white-pressed);
  --background-color-discovery: var(--color-background-discovery-default);
  --background-color-discovery-hovered: var(--color-background-discovery-hovered);
  --background-color-discovery-pressed: var(--color-background-discovery-pressed);
  --background-color-discovery-alpha-bold: var(--color-background-discovery-alpha-bold-default);
  --background-color-discovery-alpha-bold-hovered: var(--color-background-discovery-alpha-bold-hovered);
  --background-color-discovery-alpha-bold-pressed: var(--color-background-discovery-alpha-bold-pressed);
  --background-color-discovery-alpha-subtle: var(--color-background-discovery-alpha-subtle-default);
  --background-color-discovery-alpha-subtle-hovered: var(--color-background-discovery-alpha-subtle-hovered);
  --background-color-discovery-alpha-subtle-pressed: var(--color-background-discovery-alpha-subtle-pressed);
  --background-color-discovery-bold: var(--color-background-discovery-bold-default);
  --background-color-discovery-bold-hovered: var(--color-background-discovery-bold-hovered);
  --background-color-discovery-bold-pressed: var(--color-background-discovery-bold-pressed);
  --background-color-discovery-bolder: var(--color-background-discovery-bolder-default);
  --background-color-discovery-bolder-hovered: var(--color-background-discovery-bolder-hovered);
  --background-color-discovery-bolder-pressed: var(--color-background-discovery-bolder-pressed);
  --background-color-discovery-subtle: var(--color-background-discovery-subtle-default);
  --background-color-discovery-subtle-hovered: var(--color-background-discovery-subtle-hovered);
  --background-color-discovery-subtle-pressed: var(--color-background-discovery-subtle-pressed);
  --background-color-discovery-subtlest: var(--color-background-discovery-subtlest-default);
  --background-color-discovery-subtlest-hovered: var(--color-background-discovery-subtlest-hovered);
  --background-color-discovery-subtlest-pressed: var(--color-background-discovery-subtlest-pressed);
  --background-color-discovery-white: var(--color-background-discovery-white-default);
  --background-color-discovery-white-hovered: var(--color-background-discovery-white-hovered);
  --background-color-discovery-white-pressed: var(--color-background-discovery-white-pressed);
  --background-color-info: var(--color-background-info-default);
  --background-color-info-hovered: var(--color-background-info-hovered);
  --background-color-info-pressed: var(--color-background-info-pressed);
  --background-color-info-alpha-bold: var(--color-background-info-alpha-bold-default);
  --background-color-info-alpha-bold-hovered: var(--color-background-info-alpha-bold-hovered);
  --background-color-info-alpha-bold-pressed: var(--color-background-info-alpha-bold-pressed);
  --background-color-info-alpha-subtle: var(--color-background-info-alpha-subtle-default);
  --background-color-info-alpha-subtle-hovered: var(--color-background-info-alpha-subtle-hovered);
  --background-color-info-alpha-subtle-pressed: var(--color-background-info-alpha-subtle-pressed);
  --background-color-info-bold: var(--color-background-info-bold-default);
  --background-color-info-bold-hovered: var(--color-background-info-bold-hovered);
  --background-color-info-bold-pressed: var(--color-background-info-bold-pressed);
  --background-color-info-bolder: var(--color-background-info-bolder-default);
  --background-color-info-bolder-hovered: var(--color-background-info-bolder-hovered);
  --background-color-info-bolder-pressed: var(--color-background-info-bolder-pressed);
  --background-color-info-subtle: var(--color-background-info-subtle-default);
  --background-color-info-subtle-hovered: var(--color-background-info-subtle-hovered);
  --background-color-info-subtle-pressed: var(--color-background-info-subtle-pressed);
  --background-color-info-subtlest: var(--color-background-info-subtlest-default);
  --background-color-info-subtlest-hovered: var(--color-background-info-subtlest-hovered);
  --background-color-info-subtlest-pressed: var(--color-background-info-subtlest-pressed);
  --background-color-info-white: var(--color-background-info-white-default);
  --background-color-info-white-hovered: var(--color-background-info-white-hovered);
  --background-color-info-white-pressed: var(--color-background-info-white-pressed);
  --background-color-input: var(--color-background-input-default);
  --background-color-input-disabled: var(--color-background-input-disabled);
  --background-color-input-hovered: var(--color-background-input-hovered);
  --background-color-input-pressed: var(--color-background-input-pressed);
  --background-color-input-danger: var(--color-background-input-danger-default);
  --background-color-input-danger-hovered: var(--color-background-input-danger-hovered);
  --background-color-input-danger-pressed: var(--color-background-input-danger-pressed);
  --background-color-input-selected: var(--color-background-input-selected-default);
  --background-color-input-selected-hovered: var(--color-background-input-selected-hovered);
  --background-color-input-selected-pressed: var(--color-background-input-selected-pressed);
  --background-color-neutral: var(--color-background-neutral-default);
  --background-color-neutral-hovered: var(--color-background-neutral-hovered);
  --background-color-neutral-pressed: var(--color-background-neutral-pressed);
  --background-color-neutral-alpha-bold: var(--color-background-neutral-alpha-bold-default);
  --background-color-neutral-alpha-bold-hovered: var(--color-background-neutral-alpha-bold-hovered);
  --background-color-neutral-alpha-bold-pressed: var(--color-background-neutral-alpha-bold-pressed);
  --background-color-neutral-alpha-bold-inverted: var(--color-background-neutral-alpha-bold-inverted-default);
  --background-color-neutral-alpha-bold-inverted-hovered: var(--color-background-neutral-alpha-bold-inverted-hovered);
  --background-color-neutral-alpha-bold-inverted-pressed: var(--color-background-neutral-alpha-bold-inverted-pressed);
  --background-color-neutral-alpha-default: var(--color-background-neutral-alpha-default-pressed);
  --background-color-neutral-alpha-default-inverted: var(--color-background-neutral-alpha-default-inverted-pressed);
  --background-color-neutral-alpha-subltest: var(--color-background-neutral-alpha-subltest-default);
  --background-color-neutral-alpha-subltest-hovered: var(--color-background-neutral-alpha-subltest-hovered);
  --background-color-neutral-alpha-subltest-pressed: var(--color-background-neutral-alpha-subltest-pressed);
  --background-color-neutral-alpha-subltest-inverted: var(--color-background-neutral-alpha-subltest-inverted-default);
  --background-color-neutral-alpha-subltest-inverted-hovered: var(
    --color-background-neutral-alpha-subltest-inverted-hovered
  );
  --background-color-neutral-alpha-subltest-inverted-pressed: var(
    --color-background-neutral-alpha-subltest-inverted-pressed
  );
  --background-color-neutral-alpha-subtle: var(--color-background-neutral-alpha-subtle-default);
  --background-color-neutral-alpha-subtle-hovered: var(--color-background-neutral-alpha-subtle-hovered);
  --background-color-neutral-alpha-subtle-pressed: var(--color-background-neutral-alpha-subtle-pressed);
  --background-color-neutral-alpha-subtle-inverted: var(--color-background-neutral-alpha-subtle-inverted-default);
  --background-color-neutral-alpha-subtle-inverted-hovered: var(
    --color-background-neutral-alpha-subtle-inverted-hovered
  );
  --background-color-neutral-alpha-subtle-inverted-pressed: var(
    --color-background-neutral-alpha-subtle-inverted-pressed
  );
  --background-color-neutral-bold: var(--color-background-neutral-bold-default);
  --background-color-neutral-bold-hovered: var(--color-background-neutral-bold-hovered);
  --background-color-neutral-bold-pressed: var(--color-background-neutral-bold-pressed);
  --background-color-neutral-bolder: var(--color-background-neutral-bolder-default);
  --background-color-neutral-bolder-hovered: var(--color-background-neutral-bolder-hovered);
  --background-color-neutral-bolder-pressed: var(--color-background-neutral-bolder-pressed);
  --background-color-neutral-subtle: var(--color-background-neutral-subtle-default);
  --background-color-neutral-subtle-hovered: var(--color-background-neutral-subtle-hovered);
  --background-color-neutral-subtle-pressed: var(--color-background-neutral-subtle-pressed);
  --background-color-neutral-subtlest: var(--color-background-neutral-subtlest-default);
  --background-color-neutral-subtlest-hovered: var(--color-background-neutral-subtlest-hovered);
  --background-color-neutral-subtlest-pressed: var(--color-background-neutral-subtlest-pressed);
  --background-color-neutral-white: var(--color-background-neutral-white-default);
  --background-color-neutral-white-hovered: var(--color-background-neutral-white-hovered);
  --background-color-neutral-white-pressed: var(--color-background-neutral-white-pressed);
  --background-color-selected: var(--color-background-selected-default);
  --background-color-selected-hovered: var(--color-background-selected-hovered);
  --background-color-selected-pressed: var(--color-background-selected-pressed);
  --background-color-selected-alpha: var(--color-background-selected-alpha-default);
  --background-color-selected-alpha-hovered: var(--color-background-selected-alpha-hovered);
  --background-color-selected-alpha-pressed: var(--color-background-selected-alpha-pressed);
  --background-color-selected-bold: var(--color-background-selected-bold-default);
  --background-color-selected-bold-hovered: var(--color-background-selected-bold-hovered);
  --background-color-selected-bold-pressed: var(--color-background-selected-bold-pressed);
  --background-color-selected-bolder: var(--color-background-selected-bolder-default);
  --background-color-selected-bolder-hovered: var(--color-background-selected-bolder-hovered);
  --background-color-selected-bolder-pressed: var(--color-background-selected-bolder-pressed);
  --background-color-selected-subtle: var(--color-background-selected-subtle-default);
  --background-color-selected-subtle-hovered: var(--color-background-selected-subtle-hovered);
  --background-color-selected-subtle-pressed: var(--color-background-selected-subtle-pressed);
  --background-color-selected-white: var(--color-background-selected-white-default);
  --background-color-selected-white-hovered: var(--color-background-selected-white-hovered);
  --background-color-selected-white-pressed: var(--color-background-selected-white-pressed);
  --background-color-success: var(--color-background-success-default);
  --background-color-success-hovered: var(--color-background-success-hovered);
  --background-color-success-pressed: var(--color-background-success-pressed);
  --background-color-success-alpha-bold: var(--color-background-success-alpha-bold-default);
  --background-color-success-alpha-bold-hovered: var(--color-background-success-alpha-bold-hovered);
  --background-color-success-alpha-bold-pressed: var(--color-background-success-alpha-bold-pressed);
  --background-color-success-alpha-subtle: var(--color-background-success-alpha-subtle-default);
  --background-color-success-alpha-subtle-hovered: var(--color-background-success-alpha-subtle-hovered);
  --background-color-success-alpha-subtle-pressed: var(--color-background-success-alpha-subtle-pressed);
  --background-color-success-bold: var(--color-background-success-bold-default);
  --background-color-success-bold-hovered: var(--color-background-success-bold-hovered);
  --background-color-success-bold-pressed: var(--color-background-success-bold-pressed);
  --background-color-success-bolder: var(--color-background-success-bolder-default);
  --background-color-success-bolder-hovered: var(--color-background-success-bolder-hovered);
  --background-color-success-bolder-pressed: var(--color-background-success-bolder-pressed);
  --background-color-success-subtle: var(--color-background-success-subtle-default);
  --background-color-success-subtle-hovered: var(--color-background-success-subtle-hovered);
  --background-color-success-subtle-pressed: var(--color-background-success-subtle-pressed);
  --background-color-success-subtlest: var(--color-background-success-subtlest-default);
  --background-color-success-subtlest-hovered: var(--color-background-success-subtlest-hovered);
  --background-color-success-subtlest-pressed: var(--color-background-success-subtlest-pressed);
  --background-color-success-white: var(--color-background-success-white-default);
  --background-color-success-white-hovered: var(--color-background-success-white-hovered);
  --background-color-success-white-pressed: var(--color-background-success-white-pressed);
  --background-color-warning: var(--color-background-warning-default);
  --background-color-warning-hovered: var(--color-background-warning-hovered);
  --background-color-warning-pressed: var(--color-background-warning-pressed);
  --background-color-warning-alpha-bold: var(--color-background-warning-alpha-bold-default);
  --background-color-warning-alpha-bold-hovered: var(--color-background-warning-alpha-bold-hovered);
  --background-color-warning-alpha-bold-pressed: var(--color-background-warning-alpha-bold-pressed);
  --background-color-warning-alpha-subtle: var(--color-background-warning-alpha-subtle-default);
  --background-color-warning-alpha-subtle-hovered: var(--color-background-warning-alpha-subtle-hovered);
  --background-color-warning-alpha-subtle-pressed: var(--color-background-warning-alpha-subtle-pressed);
  --background-color-warning-bold: var(--color-background-warning-bold-default);
  --background-color-warning-bold-hovered: var(--color-background-warning-bold-hovered);
  --background-color-warning-bold-pressed: var(--color-background-warning-bold-pressed);
  --background-color-warning-bolder: var(--color-background-warning-bolder-default);
  --background-color-warning-bolder-hovered: var(--color-background-warning-bolder-hovered);
  --background-color-warning-bolder-pressed: var(--color-background-warning-bolder-pressed);
  --background-color-warning-subtle: var(--color-background-warning-subtle-default);
  --background-color-warning-subtle-hovered: var(--color-background-warning-subtle-hovered);
  --background-color-warning-subtle-pressed: var(--color-background-warning-subtle-pressed);
  --background-color-warning-subtlest: var(--color-background-warning-subtlest-default);
  --background-color-warning-subtlest-hovered: var(--color-background-warning-subtlest-hovered);
  --background-color-warning-subtlest-pressed: var(--color-background-warning-subtlest-pressed);
  --background-color-warning-white: var(--color-background-warning-white-default);
  --background-color-warning-white-hovered: var(--color-background-warning-white-hovered);
  --background-color-warning-white-pressed: var(--color-background-warning-white-pressed);
  --background-color-overlay: var(--color-overlay-default);
  --background-color-overlay-subtle: var(--color-overlay-subtle);
  --background-color-overlay-inverse: var(--color-overlay-inverse-default);
  --background-color-overlay-inverse-subtle: var(--color-overlay-inverse-subtle);
  --background-color-skeleton: var(--color-skeleton-default);
  --background-color-skeleton-subtle: var(--color-skeleton-subtle);
  --background-color-surface: var(--color-surface-default);
  --background-color-surface-hovered: var(--color-surface-hovered);
  --background-color-surface-pressed: var(--color-surface-pressed);
  --background-color-surface-inverse: var(--color-surface-inverse-default);
  --background-color-surface-inverse-hovered: var(--color-surface-inverse-hovered);
  --background-color-surface-inverse-pressed: var(--color-surface-inverse-pressed);
  --background-color-surface-navigation: var(--color-surface-navigation-default);
  --background-color-surface-navigation-hovered: var(--color-surface-navigation-hovered);
  --background-color-surface-navigation-pressed: var(--color-surface-navigation-pressed);
  --background-color-surface-overlay: var(--color-surface-overlay-default);
  --background-color-surface-overlay-hovered: var(--color-surface-overlay-hovered);
  --background-color-surface-overlay-pressed: var(--color-surface-overlay-pressed);
  --background-color-surface-raised: var(--color-surface-raised-default);
  --background-color-surface-raised-hovered: var(--color-surface-raised-hovered);
  --background-color-surface-raised-pressed: var(--color-surface-raised-pressed);
  --background-color-surface-sunken: var(--color-surface-sunken-default);

  --border-color-accent-blue-bold: var(--color-border-accent-blue-bold);
  --border-color-accent-blue: var(--color-border-accent-blue-default);
  --border-color-accent-blue-subtle: var(--color-border-accent-blue-subtle);
  --border-color-accent-blue-subtlest: var(--color-border-accent-blue-subtlest);
  --border-color-accent-cyan-bold: var(--color-border-accent-cyan-bold);
  --border-color-accent-cyan: var(--color-border-accent-cyan-default);
  --border-color-accent-cyan-subtle: var(--color-border-accent-cyan-subtle);
  --border-color-accent-cyan-subtlest: var(--color-border-accent-cyan-subtlest);
  --border-color-accent-emerald-bold: var(--color-border-accent-emerald-bold);
  --border-color-accent-emerald: var(--color-border-accent-emerald-default);
  --border-color-accent-emerald-subtle: var(--color-border-accent-emerald-subtle);
  --border-color-accent-emerald-subtlest: var(--color-border-accent-emerald-subtlest);
  --border-color-accent-fuchsia-bold: var(--color-border-accent-fuchsia-bold);
  --border-color-accent-fuchsia: var(--color-border-accent-fuchsia-default);
  --border-color-accent-fuchsia-subtle: var(--color-border-accent-fuchsia-subtle);
  --border-color-accent-fuchsia-subtlest: var(--color-border-accent-fuchsia-subtlest);
  --border-color-accent-green-bold: var(--color-border-accent-green-bold);
  --border-color-accent-green: var(--color-border-accent-green-default);
  --border-color-accent-green-subtle: var(--color-border-accent-green-subtle);
  --border-color-accent-green-subtlest: var(--color-border-accent-green-subtlest);
  --border-color-accent-indigo-bold: var(--color-border-accent-indigo-bold);
  --border-color-accent-indigo: var(--color-border-accent-indigo-default);
  --border-color-accent-indigo-subtle: var(--color-border-accent-indigo-subtle);
  --border-color-accent-indigo-subtlest: var(--color-border-accent-indigo-subtlest);
  --border-color-accent-lime-bold: var(--color-border-accent-lime-bold);
  --border-color-accent-lime: var(--color-border-accent-lime-default);
  --border-color-accent-lime-subtle: var(--color-border-accent-lime-subtle);
  --border-color-accent-lime-subtlest: var(--color-border-accent-lime-subtlest);
  --border-color-accent-orange-bold: var(--color-border-accent-orange-bold);
  --border-color-accent-orange: var(--color-border-accent-orange-default);
  --border-color-accent-orange-subtle: var(--color-border-accent-orange-subtle);
  --border-color-accent-orange-subtlest: var(--color-border-accent-orange-subtlest);
  --border-color-accent-pink-bold: var(--color-border-accent-pink-bold);
  --border-color-accent-pink: var(--color-border-accent-pink-default);
  --border-color-accent-pink-subtle: var(--color-border-accent-pink-subtle);
  --border-color-accent-pink-subtlest: var(--color-border-accent-pink-subtlest);
  --border-color-accent-purple-bold: var(--color-border-accent-purple-bold);
  --border-color-accent-purple: var(--color-border-accent-purple-default);
  --border-color-accent-purple-subtle: var(--color-border-accent-purple-subtle);
  --border-color-accent-purple-subtlest: var(--color-border-accent-purple-subtlest);
  --border-color-accent-red-bold: var(--color-border-accent-red-bold);
  --border-color-accent-red: var(--color-border-accent-red-default);
  --border-color-accent-red-subtle: var(--color-border-accent-red-subtle);
  --border-color-accent-red-subtlest: var(--color-border-accent-red-subtlest);
  --border-color-accent-rose-bold: var(--color-border-accent-rose-bold);
  --border-color-accent-rose: var(--color-border-accent-rose-default);
  --border-color-accent-rose-subtle: var(--color-border-accent-rose-subtle);
  --border-color-accent-rose-subtlest: var(--color-border-accent-rose-subtlest);
  --border-color-accent-rosedust-bold: var(--color-border-accent-rosedust-bold);
  --border-color-accent-rosedust: var(--color-border-accent-rosedust-default);
  --border-color-accent-rosedust-subtle: var(--color-border-accent-rosedust-subtle);
  --border-color-accent-rosedust-subtlest: var(--color-border-accent-rosedust-subtlest);
  --border-color-accent-sky-bold: var(--color-border-accent-sky-bold);
  --border-color-accent-sky: var(--color-border-accent-sky-default);
  --border-color-accent-sky-subtle: var(--color-border-accent-sky-subtle);
  --border-color-accent-sky-subtlest: var(--color-border-accent-sky-subtlest);
  --border-color-accent-teal-bold: var(--color-border-accent-teal-bold);
  --border-color-accent-teal: var(--color-border-accent-teal-default);
  --border-color-accent-teal-subtle: var(--color-border-accent-teal-subtle);
  --border-color-accent-teal-subtlest: var(--color-border-accent-teal-subtlest);
  --border-color-accent-yellow-bold: var(--color-border-accent-yellow-bold);
  --border-color-accent-yellow: var(--color-border-accent-yellow-default);
  --border-color-accent-yellow-subtle: var(--color-border-accent-yellow-subtle);
  --border-color-accent-yellow-subtlest: var(--color-border-accent-yellow-subtlest);
  --border-color-brand-bold: var(--color-border-brand-bold);
  --border-color-brand: var(--color-border-brand-default);
  --border-color-brand-subtle: var(--color-border-brand-subtle);
  --border-color-brand-subtlest: var(--color-border-brand-subtlest);
  --border-color-danger-bold: var(--color-border-danger-bold);
  --border-color-danger: var(--color-border-danger-default);
  --border-color-danger-subtle: var(--color-border-danger-subtle);
  --border-color-danger-subtlest: var(--color-border-danger-subtlest);
  --border-color-discovery-bold: var(--color-border-discovery-bold);
  --border-color-discovery: var(--color-border-discovery-default);
  --border-color-discovery-subtle: var(--color-border-discovery-subtle);
  --border-color-discovery-subtlest: var(--color-border-discovery-subtlest);
  --border-color-info-bold: var(--color-border-info-bold);
  --border-color-info: var(--color-border-info-default);
  --border-color-info-subtle: var(--color-border-info-subtle);
  --border-color-info-subtlest: var(--color-border-info-subtlest);
  --border-color-input-danger: var(--color-border-input-danger);
  --border-color-input: var(--color-border-input-default);
  --border-color-input-disabled: var(--color-border-input-disabled);
  --border-color-input-hovered: var(--color-border-input-hovered);
  --border-color-input-selected: var(--color-border-input-selected);
  --border-color-neutral: var(--color-border-neutral-default);
  --border-color-neutral-disabled: var(--color-border-neutral-disabled);
  --border-color-neutral-hovered: var(--color-border-neutral-hovered);
  --border-color-neutral-inverse: var(--color-border-neutral-inverse);
  --border-color-neutral-bold: var(--color-border-neutral-bold-default);
  --border-color-neutral-bold-hovered: var(--color-border-neutral-bold-hovered);
  --border-color-neutral-subtle: var(--color-border-neutral-subtle-default);
  --border-color-neutral-subtle-hovered: var(--color-border-neutral-subtle-hovered);
  --border-color-neutral-subtlest: var(--color-border-neutral-subtlest-default);
  --border-color-neutral-subtlest-hovered: var(--color-border-neutral-subtlest-hovered);
  --border-color-selected: var(--color-border-selected-default);
  --border-color-selected-hover: var(--color-border-selected-hover);
  --border-color-selected-subtle: var(--color-border-selected-subtle-default);
  --border-color-selected-subtle-hover: var(--color-border-selected-subtle-hover);
  --border-color-selected-subtlest: var(--color-border-selected-subtlest-default);
  --border-color-selected-subtlest-hover: var(--color-border-selected-subtlest-hover);
  --border-color-success-bold: var(--color-border-success-bold);
  --border-color-success: var(--color-border-success-default);
  --border-color-success-subtle: var(--color-border-success-subtle);
  --border-color-success-subtlest: var(--color-border-success-subtlest);
  --border-color-warning-bold: var(--color-border-warning-bold);
  --border-color-warning: var(--color-border-warning-default);
  --border-color-warning-subtle: var(--color-border-warning-subtle);
  --border-color-warning-subtlest: var(--color-border-warning-subtlest);

  --text-color-icon-accent-blue-bold: var(--color-icon-accent-blue-bold);
  --text-color-icon-accent-blue: var(--color-icon-accent-blue-default);
  --text-color-icon-accent-blue-inverse: var(--color-icon-accent-blue-inverse);
  --text-color-icon-accent-blue-subtle: var(--color-icon-accent-blue-subtle);
  --text-color-icon-accent-blue-subtlest: var(--color-icon-accent-blue-subtlest);
  --text-color-icon-accent-cyan-bold: var(--color-icon-accent-cyan-bold);
  --text-color-icon-accent-cyan: var(--color-icon-accent-cyan-default);
  --text-color-icon-accent-cyan-inverse: var(--color-icon-accent-cyan-inverse);
  --text-color-icon-accent-cyan-subtle: var(--color-icon-accent-cyan-subtle);
  --text-color-icon-accent-cyan-subtlest: var(--color-icon-accent-cyan-subtlest);
  --text-color-icon-accent-emerald-bold: var(--color-icon-accent-emerald-bold);
  --text-color-icon-accent-emerald: var(--color-icon-accent-emerald-default);
  --text-color-icon-accent-emerald-inverse: var(--color-icon-accent-emerald-inverse);
  --text-color-icon-accent-emerald-subtle: var(--color-icon-accent-emerald-subtle);
  --text-color-icon-accent-emerald-subtlest: var(--color-icon-accent-emerald-subtlest);
  --text-color-icon-accent-fuchsia-bold: var(--color-icon-accent-fuchsia-bold);
  --text-color-icon-accent-fuchsia: var(--color-icon-accent-fuchsia-default);
  --text-color-icon-accent-fuchsia-inverse: var(--color-icon-accent-fuchsia-inverse);
  --text-color-icon-accent-fuchsia-subtle: var(--color-icon-accent-fuchsia-subtle);
  --text-color-icon-accent-fuchsia-subtlest: var(--color-icon-accent-fuchsia-subtlest);
  --text-color-icon-accent-green-bold: var(--color-icon-accent-green-bold);
  --text-color-icon-accent-green: var(--color-icon-accent-green-default);
  --text-color-icon-accent-green-inverse: var(--color-icon-accent-green-inverse);
  --text-color-icon-accent-green-subtle: var(--color-icon-accent-green-subtle);
  --text-color-icon-accent-green-subtlest: var(--color-icon-accent-green-subtlest);
  --text-color-icon-accent-indigo-bold: var(--color-icon-accent-indigo-bold);
  --text-color-icon-accent-indigo: var(--color-icon-accent-indigo-default);
  --text-color-icon-accent-indigo-inverse: var(--color-icon-accent-indigo-inverse);
  --text-color-icon-accent-indigo-subtle: var(--color-icon-accent-indigo-subtle);
  --text-color-icon-accent-indigo-subtlest: var(--color-icon-accent-indigo-subtlest);
  --text-color-icon-accent-lime-bold: var(--color-icon-accent-lime-bold);
  --text-color-icon-accent-lime: var(--color-icon-accent-lime-default);
  --text-color-icon-accent-lime-inverse: var(--color-icon-accent-lime-inverse);
  --text-color-icon-accent-lime-subtle: var(--color-icon-accent-lime-subtle);
  --text-color-icon-accent-lime-subtlest: var(--color-icon-accent-lime-subtlest);
  --text-color-icon-accent-orange-bold: var(--color-icon-accent-orange-bold);
  --text-color-icon-accent-orange: var(--color-icon-accent-orange-default);
  --text-color-icon-accent-orange-inverse: var(--color-icon-accent-orange-inverse);
  --text-color-icon-accent-orange-subtle: var(--color-icon-accent-orange-subtle);
  --text-color-icon-accent-orange-subtlest: var(--color-icon-accent-orange-subtlest);
  --text-color-icon-accent-pink-bold: var(--color-icon-accent-pink-bold);
  --text-color-icon-accent-pink: var(--color-icon-accent-pink-default);
  --text-color-icon-accent-pink-inverse: var(--color-icon-accent-pink-inverse);
  --text-color-icon-accent-pink-subtle: var(--color-icon-accent-pink-subtle);
  --text-color-icon-accent-pink-subtlest: var(--color-icon-accent-pink-subtlest);
  --text-color-icon-accent-purple-bold: var(--color-icon-accent-purple-bold);
  --text-color-icon-accent-purple: var(--color-icon-accent-purple-default);
  --text-color-icon-accent-purple-inverse: var(--color-icon-accent-purple-inverse);
  --text-color-icon-accent-purple-subtle: var(--color-icon-accent-purple-subtle);
  --text-color-icon-accent-purple-subtlest: var(--color-icon-accent-purple-subtlest);
  --text-color-icon-accent-red-bold: var(--color-icon-accent-red-bold);
  --text-color-icon-accent-red: var(--color-icon-accent-red-default);
  --text-color-icon-accent-red-inverse: var(--color-icon-accent-red-inverse);
  --text-color-icon-accent-red-subtle: var(--color-icon-accent-red-subtle);
  --text-color-icon-accent-red-subtlest: var(--color-icon-accent-red-subtlest);
  --text-color-icon-accent-rose-bold: var(--color-icon-accent-rose-bold);
  --text-color-icon-accent-rose: var(--color-icon-accent-rose-default);
  --text-color-icon-accent-rose-inverse: var(--color-icon-accent-rose-inverse);
  --text-color-icon-accent-rose-subtle: var(--color-icon-accent-rose-subtle);
  --text-color-icon-accent-rose-subtlest: var(--color-icon-accent-rose-subtlest);
  --text-color-icon-accent-rosedust-bold: var(--color-icon-accent-rosedust-bold);
  --text-color-icon-accent-rosedust: var(--color-icon-accent-rosedust-default);
  --text-color-icon-accent-rosedust-inverse: var(--color-icon-accent-rosedust-inverse);
  --text-color-icon-accent-rosedust-subtle: var(--color-icon-accent-rosedust-subtle);
  --text-color-icon-accent-rosedust-subtlest: var(--color-icon-accent-rosedust-subtlest);
  --text-color-icon-accent-sky-bold: var(--color-icon-accent-sky-bold);
  --text-color-icon-accent-sky: var(--color-icon-accent-sky-default);
  --text-color-icon-accent-sky-inverse: var(--color-icon-accent-sky-inverse);
  --text-color-icon-accent-sky-subtle: var(--color-icon-accent-sky-subtle);
  --text-color-icon-accent-sky-subtlest: var(--color-icon-accent-sky-subtlest);
  --text-color-icon-accent-teal-bold: var(--color-icon-accent-teal-bold);
  --text-color-icon-accent-teal: var(--color-icon-accent-teal-default);
  --text-color-icon-accent-teal-inverse: var(--color-icon-accent-teal-inverse);
  --text-color-icon-accent-teal-subtle: var(--color-icon-accent-teal-subtle);
  --text-color-icon-accent-teal-subtlest: var(--color-icon-accent-teal-subtlest);
  --text-color-icon-accent-yellow-bold: var(--color-icon-accent-yellow-bold);
  --text-color-icon-accent-yellow: var(--color-icon-accent-yellow-default);
  --text-color-icon-accent-yellow-inverse: var(--color-icon-accent-yellow-inverse);
  --text-color-icon-accent-yellow-subtle: var(--color-icon-accent-yellow-subtle);
  --text-color-icon-accent-yellow-subtlest: var(--color-icon-accent-yellow-subtlest);
  --text-color-icon-brand-bold: var(--color-icon-brand-bold);
  --text-color-icon-brand: var(--color-icon-brand-default);
  --text-color-icon-brand-inverse: var(--color-icon-brand-inverse);
  --text-color-icon-brand-subtle: var(--color-icon-brand-subtle);
  --text-color-icon-brand-subtlest: var(--color-icon-brand-subtlest);
  --text-color-icon-danger-bold: var(--color-icon-danger-bold);
  --text-color-icon-danger: var(--color-icon-danger-default);
  --text-color-icon-danger-inverse: var(--color-icon-danger-inverse);
  --text-color-icon-danger-subtle: var(--color-icon-danger-subtle);
  --text-color-icon-danger-subtlest: var(--color-icon-danger-subtlest);
  --text-color-icon-discovery-bold: var(--color-icon-discovery-bold);
  --text-color-icon-discovery: var(--color-icon-discovery-default);
  --text-color-icon-discovery-inverse: var(--color-icon-discovery-inverse);
  --text-color-icon-discovery-subtle: var(--color-icon-discovery-subtle);
  --text-color-icon-discovery-subtlest: var(--color-icon-discovery-subtlest);
  --text-color-icon-info-bold: var(--color-icon-info-bold);
  --text-color-icon-info: var(--color-icon-info-default);
  --text-color-icon-info-inverse: var(--color-icon-info-inverse);
  --text-color-icon-info-subtle: var(--color-icon-info-subtle);
  --text-color-icon-info-subtlest: var(--color-icon-info-subtlest);
  --text-color-icon-neutral-bold: var(--color-icon-neutral-bold);
  --text-color-icon-neutral: var(--color-icon-neutral-default);
  --text-color-icon-neutral-inverse: var(--color-icon-neutral-inverse);
  --text-color-icon-neutral-subtle: var(--color-icon-neutral-subtle);
  --text-color-icon-neutral-subtlest: var(--color-icon-neutral-subtlest);
  --text-color-icon-selected-bold: var(--color-icon-selected-bold);
  --text-color-icon-selected: var(--color-icon-selected-default);
  --text-color-icon-selected-inverse: var(--color-icon-selected-inverse);
  --text-color-icon-selected-subtle: var(--color-icon-selected-subtle);
  --text-color-icon-selected-subtlest: var(--color-icon-selected-subtlest);
  --text-color-icon-success-bold: var(--color-icon-success-bold);
  --text-color-icon-success: var(--color-icon-success-default);
  --text-color-icon-success-inverse: var(--color-icon-success-inverse);
  --text-color-icon-success-subtle: var(--color-icon-success-subtle);
  --text-color-icon-success-subtlest: var(--color-icon-success-subtlest);
  --text-color-icon-warning-bold: var(--color-icon-warning-bold);
  --text-color-icon-warning: var(--color-icon-warning-default);
  --text-color-icon-warning-inverse: var(--color-icon-warning-inverse);
  --text-color-icon-warning-subtle: var(--color-icon-warning-subtle);
  --text-color-icon-warning-subtlest: var(--color-icon-warning-subtlest);
  --text-color-interaction-hovered: var(--color-interaction-hovered);
  --text-color-interaction-pressed: var(--color-interaction-pressed);
  --text-color-link-brand: var(--color-link-brand-default);
  --text-color-link-brand-hovered: var(--color-link-brand-hovered);
  --text-color-link-brand-pressed: var(--color-link-brand-pressed);
  --text-color-link-danger: var(--color-link-danger-default);
  --text-color-link-danger-hovered: var(--color-link-danger-hovered);
  --text-color-link-danger-pressed: var(--color-link-danger-pressed);
  --text-color-link-discovery: var(--color-link-discovery-default);
  --text-color-link-discovery-hovered: var(--color-link-discovery-hovered);
  --text-color-link-discovery-pressed: var(--color-link-discovery-pressed);
  --text-color-link-info: var(--color-link-info-default);
  --text-color-link-info-hovered: var(--color-link-info-hovered);
  --text-color-link-info-pressed: var(--color-link-info-pressed);
  --text-color-link-inverse: var(--color-link-inverse-default);
  --text-color-link-inverse-hovered: var(--color-link-inverse-hovered);
  --text-color-link-inverse-pressed: var(--color-link-inverse-pressed);
  --text-color-link-neutral: var(--color-link-neutral-default);
  --text-color-link-neutral-hovered: var(--color-link-neutral-hovered);
  --text-color-link-neutral-pressed: var(--color-link-neutral-pressed);
  --text-color-link-success: var(--color-link-success-default);
  --text-color-link-success-hovered: var(--color-link-success-hovered);
  --text-color-link-success-pressed: var(--color-link-success-pressed);
  --text-color-link-warning: var(--color-link-warning-default);
  --text-color-link-warning-hovered: var(--color-link-warning-hovered);
  --text-color-link-warning-pressed: var(--color-link-warning-pressed);
  --text-color-accent-blue: var(--color-text-accent-blue-default);
  --text-color-accent-blue-inverse: var(--color-text-accent-blue-inverse);
  --text-color-accent-blue-subtle: var(--color-text-accent-blue-subtle);
  --text-color-accent-cyan: var(--color-text-accent-cyan-default);
  --text-color-accent-cyan-inverse: var(--color-text-accent-cyan-inverse);
  --text-color-accent-cyan-subtle: var(--color-text-accent-cyan-subtle);
  --text-color-accent-emerald: var(--color-text-accent-emerald-default);
  --text-color-accent-emerald-inverse: var(--color-text-accent-emerald-inverse);
  --text-color-accent-emerald-subtle: var(--color-text-accent-emerald-subtle);
  --text-color-accent-fuchsia: var(--color-text-accent-fuchsia-default);
  --text-color-accent-fuchsia-inverse: var(--color-text-accent-fuchsia-inverse);
  --text-color-accent-fuchsia-subtle: var(--color-text-accent-fuchsia-subtle);
  --text-color-accent-green: var(--color-text-accent-green-default);
  --text-color-accent-green-inverse: var(--color-text-accent-green-inverse);
  --text-color-accent-green-subtle: var(--color-text-accent-green-subtle);
  --text-color-accent-indigo: var(--color-text-accent-indigo-default);
  --text-color-accent-indigo-inverse: var(--color-text-accent-indigo-inverse);
  --text-color-accent-indigo-subtle: var(--color-text-accent-indigo-subtle);
  --text-color-accent-lime: var(--color-text-accent-lime-default);
  --text-color-accent-lime-inverse: var(--color-text-accent-lime-inverse);
  --text-color-accent-lime-subtle: var(--color-text-accent-lime-subtle);
  --text-color-accent-orange: var(--color-text-accent-orange-default);
  --text-color-accent-orange-inverse: var(--color-text-accent-orange-inverse);
  --text-color-accent-orange-subtle: var(--color-text-accent-orange-subtle);
  --text-color-accent-pink: var(--color-text-accent-pink-default);
  --text-color-accent-pink-inverse: var(--color-text-accent-pink-inverse);
  --text-color-accent-pink-subtle: var(--color-text-accent-pink-subtle);
  --text-color-accent-purple: var(--color-text-accent-purple-default);
  --text-color-accent-purple-inverse: var(--color-text-accent-purple-inverse);
  --text-color-accent-purple-subtle: var(--color-text-accent-purple-subtle);
  --text-color-accent-red: var(--color-text-accent-red-default);
  --text-color-accent-red-inverse: var(--color-text-accent-red-inverse);
  --text-color-accent-red-subtle: var(--color-text-accent-red-subtle);
  --text-color-accent-rose: var(--color-text-accent-rose-default);
  --text-color-accent-rose-inverse: var(--color-text-accent-rose-inverse);
  --text-color-accent-rose-subtle: var(--color-text-accent-rose-subtle);
  --text-color-accent-rosedust: var(--color-text-accent-rosedust-default);
  --text-color-accent-rosedust-inverse: var(--color-text-accent-rosedust-inverse);
  --text-color-accent-rosedust-subtle: var(--color-text-accent-rosedust-subtle);
  --text-color-accent-sky: var(--color-text-accent-sky-default);
  --text-color-accent-sky-inverse: var(--color-text-accent-sky-inverse);
  --text-color-accent-sky-subtle: var(--color-text-accent-sky-subtle);
  --text-color-accent-teal: var(--color-text-accent-teal-default);
  --text-color-accent-teal-inverse: var(--color-text-accent-teal-inverse);
  --text-color-accent-teal-subtle: var(--color-text-accent-teal-subtle);
  --text-color-accent-yellow: var(--color-text-accent-yellow-default);
  --text-color-accent-yellow-inverse: var(--color-text-accent-yellow-inverse);
  --text-color-accent-yellow-subtle: var(--color-text-accent-yellow-subtle);
  --text-color-brand-bold: var(--color-text-brand-bold);
  --text-color-brand: var(--color-text-brand-default);
  --text-color-brand-inverse: var(--color-text-brand-inverse);
  --text-color-brand-subtle: var(--color-text-brand-subtle);
  --text-color-brand-subtlest: var(--color-text-brand-subtlest);
  --text-color-danger: var(--color-text-danger-default);
  --text-color-danger-inverse: var(--color-text-danger-inverse);
  --text-color-danger-subtle: var(--color-text-danger-subtle);
  --text-color-discovery: var(--color-text-discovery-default);
  --text-color-discovery-inverse: var(--color-text-discovery-inverse);
  --text-color-discovery-subtle: var(--color-text-discovery-subtle);
  --text-color-info: var(--color-text-info-default);
  --text-color-info-inverse: var(--color-text-info-inverse);
  --text-color-info-subtle: var(--color-text-info-subtle);
  --text-color-neutral-bold: var(--color-text-neutral-bold);
  --text-color-neutral: var(--color-text-neutral-default);
  --text-color-neutral-disabled: var(--color-text-neutral-disabled);
  --text-color-neutral-inverse: var(--color-text-neutral-inverse);
  --text-color-neutral-subtle: var(--color-text-neutral-subtle);
  --text-color-neutral-subtlest: var(--color-text-neutral-subtlest);
  --text-color-selected-bold: var(--color-text-selected-bold);
  --text-color-selected: var(--color-text-selected-default);
  --text-color-selected-inverse: var(--color-text-selected-inverse);
  --text-color-selected-subtle: var(--color-text-selected-subtle);
  --text-color-selected-subtlest: var(--color-text-selected-subtlest);
  --text-color-success: var(--color-text-success-default);
  --text-color-success-inverse: var(--color-text-success-inverse);
  --text-color-success-subtle: var(--color-text-success-subtle);
  --text-color-warning: var(--color-text-warning-default);
  --text-color-warning-inverse: var(--color-text-warning-inverse);
  --text-color-warning-subtle: var(--color-text-warning-subtle);

  --stroke-accent-blue-bold: var(--color-border-accent-blue-bold);
  --stroke-accent-blue: var(--color-border-accent-blue-default);
  --stroke-accent-blue-subtle: var(--color-border-accent-blue-subtle);
  --stroke-accent-blue-subtlest: var(--color-border-accent-blue-subtlest);
  --stroke-accent-cyan-bold: var(--color-border-accent-cyan-bold);
  --stroke-accent-cyan: var(--color-border-accent-cyan-default);
  --stroke-accent-cyan-subtle: var(--color-border-accent-cyan-subtle);
  --stroke-accent-cyan-subtlest: var(--color-border-accent-cyan-subtlest);
  --stroke-accent-emerald-bold: var(--color-border-accent-emerald-bold);
  --stroke-accent-emerald: var(--color-border-accent-emerald-default);
  --stroke-accent-emerald-subtle: var(--color-border-accent-emerald-subtle);
  --stroke-accent-emerald-subtlest: var(--color-border-accent-emerald-subtlest);
  --stroke-accent-fuchsia-bold: var(--color-border-accent-fuchsia-bold);
  --stroke-accent-fuchsia: var(--color-border-accent-fuchsia-default);
  --stroke-accent-fuchsia-subtle: var(--color-border-accent-fuchsia-subtle);
  --stroke-accent-fuchsia-subtlest: var(--color-border-accent-fuchsia-subtlest);
  --stroke-accent-green-bold: var(--color-border-accent-green-bold);
  --stroke-accent-green: var(--color-border-accent-green-default);
  --stroke-accent-green-subtle: var(--color-border-accent-green-subtle);
  --stroke-accent-green-subtlest: var(--color-border-accent-green-subtlest);
  --stroke-accent-indigo-bold: var(--color-border-accent-indigo-bold);
  --stroke-accent-indigo: var(--color-border-accent-indigo-default);
  --stroke-accent-indigo-subtle: var(--color-border-accent-indigo-subtle);
  --stroke-accent-indigo-subtlest: var(--color-border-accent-indigo-subtlest);
  --stroke-accent-lime-bold: var(--color-border-accent-lime-bold);
  --stroke-accent-lime: var(--color-border-accent-lime-default);
  --stroke-accent-lime-subtle: var(--color-border-accent-lime-subtle);
  --stroke-accent-lime-subtlest: var(--color-border-accent-lime-subtlest);
  --stroke-accent-orange-bold: var(--color-border-accent-orange-bold);
  --stroke-accent-orange: var(--color-border-accent-orange-default);
  --stroke-accent-orange-subtle: var(--color-border-accent-orange-subtle);
  --stroke-accent-orange-subtlest: var(--color-border-accent-orange-subtlest);
  --stroke-accent-pink-bold: var(--color-border-accent-pink-bold);
  --stroke-accent-pink: var(--color-border-accent-pink-default);
  --stroke-accent-pink-subtle: var(--color-border-accent-pink-subtle);
  --stroke-accent-pink-subtlest: var(--color-border-accent-pink-subtlest);
  --stroke-accent-purple-bold: var(--color-border-accent-purple-bold);
  --stroke-accent-purple: var(--color-border-accent-purple-default);
  --stroke-accent-purple-subtle: var(--color-border-accent-purple-subtle);
  --stroke-accent-purple-subtlest: var(--color-border-accent-purple-subtlest);
  --stroke-accent-red-bold: var(--color-border-accent-red-bold);
  --stroke-accent-red: var(--color-border-accent-red-default);
  --stroke-accent-red-subtle: var(--color-border-accent-red-subtle);
  --stroke-accent-red-subtlest: var(--color-border-accent-red-subtlest);
  --stroke-accent-rose-bold: var(--color-border-accent-rose-bold);
  --stroke-accent-rose: var(--color-border-accent-rose-default);
  --stroke-accent-rose-subtle: var(--color-border-accent-rose-subtle);
  --stroke-accent-rose-subtlest: var(--color-border-accent-rose-subtlest);
  --stroke-accent-rosedust-bold: var(--color-border-accent-rosedust-bold);
  --stroke-accent-rosedust: var(--color-border-accent-rosedust-default);
  --stroke-accent-rosedust-subtle: var(--color-border-accent-rosedust-subtle);
  --stroke-accent-rosedust-subtlest: var(--color-border-accent-rosedust-subtlest);
  --stroke-accent-sky-bold: var(--color-border-accent-sky-bold);
  --stroke-accent-sky: var(--color-border-accent-sky-default);
  --stroke-accent-sky-subtle: var(--color-border-accent-sky-subtle);
  --stroke-accent-sky-subtlest: var(--color-border-accent-sky-subtlest);
  --stroke-accent-teal-bold: var(--color-border-accent-teal-bold);
  --stroke-accent-teal: var(--color-border-accent-teal-default);
  --stroke-accent-teal-subtle: var(--color-border-accent-teal-subtle);
  --stroke-accent-teal-subtlest: var(--color-border-accent-teal-subtlest);
  --stroke-accent-yellow-bold: var(--color-border-accent-yellow-bold);
  --stroke-accent-yellow: var(--color-border-accent-yellow-default);
  --stroke-accent-yellow-subtle: var(--color-border-accent-yellow-subtle);
  --stroke-accent-yellow-subtlest: var(--color-border-accent-yellow-subtlest);
  --stroke-brand-bold: var(--color-border-brand-bold);
  --stroke-brand: var(--color-border-brand-default);
  --stroke-brand-subtle: var(--color-border-brand-subtle);
  --stroke-brand-subtlest: var(--color-border-brand-subtlest);
  --stroke-danger-bold: var(--color-border-danger-bold);
  --stroke-danger: var(--color-border-danger-default);
  --stroke-danger-subtle: var(--color-border-danger-subtle);
  --stroke-danger-subtlest: var(--color-border-danger-subtlest);
  --stroke-discovery-bold: var(--color-border-discovery-bold);
  --stroke-discovery: var(--color-border-discovery-default);
  --stroke-discovery-subtle: var(--color-border-discovery-subtle);
  --stroke-discovery-subtlest: var(--color-border-discovery-subtlest);
  --stroke-info-bold: var(--color-border-info-bold);
  --stroke-info: var(--color-border-info-default);
  --stroke-info-subtle: var(--color-border-info-subtle);
  --stroke-info-subtlest: var(--color-border-info-subtlest);
  --stroke-input-danger: var(--color-border-input-danger);
  --stroke-input: var(--color-border-input-default);
  --stroke-input-disabled: var(--color-border-input-disabled);
  --stroke-input-hovered: var(--color-border-input-hovered);
  --stroke-input-selected: var(--color-border-input-selected);
  --stroke-neutral: var(--color-border-neutral-default);
  --stroke-neutral-disabled: var(--color-border-neutral-disabled);
  --stroke-neutral-hovered: var(--color-border-neutral-hovered);
  --stroke-neutral-inverse: var(--color-border-neutral-inverse);
  --stroke-neutral-bold: var(--color-border-neutral-bold-default);
  --stroke-neutral-bold-hovered: var(--color-border-neutral-bold-hovered);
  --stroke-neutral-subtle: var(--color-border-neutral-subtle-default);
  --stroke-neutral-subtle-hovered: var(--color-border-neutral-subtle-hovered);
  --stroke-neutral-subtlest: var(--color-border-neutral-subtlest-default);
  --stroke-neutral-subtlest-hovered: var(--color-border-neutral-subtlest-hovered);
  --stroke-selected: var(--color-border-selected-default);
  --stroke-selected-hover: var(--color-border-selected-hover);
  --stroke-selected-subtle: var(--color-border-selected-subtle-default);
  --stroke-selected-subtle-hover: var(--color-border-selected-subtle-hover);
  --stroke-selected-subtlest: var(--color-border-selected-subtlest-default);
  --stroke-selected-subtlest-hover: var(--color-border-selected-subtlest-hover);
  --stroke-success-bold: var(--color-border-success-bold);
  --stroke-success: var(--color-border-success-default);
  --stroke-success-subtle: var(--color-border-success-subtle);
  --stroke-success-subtlest: var(--color-border-success-subtlest);
  --stroke-warning-bold: var(--color-border-warning-bold);
  --stroke-warning: var(--color-border-warning-default);
  --stroke-warning-subtle: var(--color-border-warning-subtle);
  --stroke-warning-subtlest: var(--color-border-warning-subtlest);
}
