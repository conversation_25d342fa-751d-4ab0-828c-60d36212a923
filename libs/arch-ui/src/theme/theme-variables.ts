const variables = {
  borderColor: {
    'accent-blue-bold': 'var(--color-border-accent-blue-bold)',
    'accent-blue': 'var(--color-border-accent-blue-default)',
    'accent-blue-subtle': 'var(--color-border-accent-blue-subtle)',
    'accent-blue-subtlest': 'var(--color-border-accent-blue-subtlest)',
    'accent-cyan-bold': 'var(--color-border-accent-cyan-bold)',
    'accent-cyan': 'var(--color-border-accent-cyan-default)',
    'accent-cyan-subtle': 'var(--color-border-accent-cyan-subtle)',
    'accent-cyan-subtlest': 'var(--color-border-accent-cyan-subtlest)',
    'accent-emerald-bold': 'var(--color-border-accent-emerald-bold)',
    'accent-emerald': 'var(--color-border-accent-emerald-default)',
    'accent-emerald-subtle': 'var(--color-border-accent-emerald-subtle)',
    'accent-emerald-subtlest': 'var(--color-border-accent-emerald-subtlest)',
    'accent-fuchsia-bold': 'var(--color-border-accent-fuchsia-bold)',
    'accent-fuchsia': 'var(--color-border-accent-fuchsia-default)',
    'accent-fuchsia-subtle': 'var(--color-border-accent-fuchsia-subtle)',
    'accent-fuchsia-subtlest': 'var(--color-border-accent-fuchsia-subtlest)',
    'accent-green-bold': 'var(--color-border-accent-green-bold)',
    'accent-green': 'var(--color-border-accent-green-default)',
    'accent-green-subtle': 'var(--color-border-accent-green-subtle)',
    'accent-green-subtlest': 'var(--color-border-accent-green-subtlest)',
    'accent-indigo-bold': 'var(--color-border-accent-indigo-bold)',
    'accent-indigo': 'var(--color-border-accent-indigo-default)',
    'accent-indigo-subtle': 'var(--color-border-accent-indigo-subtle)',
    'accent-indigo-subtlest': 'var(--color-border-accent-indigo-subtlest)',
    'accent-lime-bold': 'var(--color-border-accent-lime-bold)',
    'accent-lime': 'var(--color-border-accent-lime-default)',
    'accent-lime-subtle': 'var(--color-border-accent-lime-subtle)',
    'accent-lime-subtlest': 'var(--color-border-accent-lime-subtlest)',
    'accent-orange-bold': 'var(--color-border-accent-orange-bold)',
    'accent-orange': 'var(--color-border-accent-orange-default)',
    'accent-orange-subtle': 'var(--color-border-accent-orange-subtle)',
    'accent-orange-subtlest': 'var(--color-border-accent-orange-subtlest)',
    'accent-pink-bold': 'var(--color-border-accent-pink-bold)',
    'accent-pink': 'var(--color-border-accent-pink-default)',
    'accent-pink-subtle': 'var(--color-border-accent-pink-subtle)',
    'accent-pink-subtlest': 'var(--color-border-accent-pink-subtlest)',
    'accent-purple-bold': 'var(--color-border-accent-purple-bold)',
    'accent-purple': 'var(--color-border-accent-purple-default)',
    'accent-purple-subtle': 'var(--color-border-accent-purple-subtle)',
    'accent-purple-subtlest': 'var(--color-border-accent-purple-subtlest)',
    'accent-red-bold': 'var(--color-border-accent-red-bold)',
    'accent-red': 'var(--color-border-accent-red-default)',
    'accent-red-subtle': 'var(--color-border-accent-red-subtle)',
    'accent-red-subtlest': 'var(--color-border-accent-red-subtlest)',
    'accent-rose-bold': 'var(--color-border-accent-rose-bold)',
    'accent-rose': 'var(--color-border-accent-rose-default)',
    'accent-rose-subtle': 'var(--color-border-accent-rose-subtle)',
    'accent-rose-subtlest': 'var(--color-border-accent-rose-subtlest)',
    'accent-rosedust-bold': 'var(--color-border-accent-rosedust-bold)',
    'accent-rosedust': 'var(--color-border-accent-rosedust-default)',
    'accent-rosedust-subtle': 'var(--color-border-accent-rosedust-subtle)',
    'accent-rosedust-subtlest': 'var(--color-border-accent-rosedust-subtlest)',
    'accent-sky-bold': 'var(--color-border-accent-sky-bold)',
    'accent-sky': 'var(--color-border-accent-sky-default)',
    'accent-sky-subtle': 'var(--color-border-accent-sky-subtle)',
    'accent-sky-subtlest': 'var(--color-border-accent-sky-subtlest)',
    'accent-teal-bold': 'var(--color-border-accent-teal-bold)',
    'accent-teal': 'var(--color-border-accent-teal-default)',
    'accent-teal-subtle': 'var(--color-border-accent-teal-subtle)',
    'accent-teal-subtlest': 'var(--color-border-accent-teal-subtlest)',
    'accent-yellow-bold': 'var(--color-border-accent-yellow-bold)',
    'accent-yellow': 'var(--color-border-accent-yellow-default)',
    'accent-yellow-subtle': 'var(--color-border-accent-yellow-subtle)',
    'accent-yellow-subtlest': 'var(--color-border-accent-yellow-subtlest)',
    'brand-bold': 'var(--color-border-brand-bold)',
    brand: 'var(--color-border-brand-default)',
    'brand-subtle': 'var(--color-border-brand-subtle)',
    'brand-subtlest': 'var(--color-border-brand-subtlest)',
    'danger-bold': 'var(--color-border-danger-bold)',
    danger: 'var(--color-border-danger-default)',
    'danger-subtle': 'var(--color-border-danger-subtle)',
    'danger-subtlest': 'var(--color-border-danger-subtlest)',
    'discovery-bold': 'var(--color-border-discovery-bold)',
    discovery: 'var(--color-border-discovery-default)',
    'discovery-subtle': 'var(--color-border-discovery-subtle)',
    'discovery-subtlest': 'var(--color-border-discovery-subtlest)',
    'info-bold': 'var(--color-border-info-bold)',
    info: 'var(--color-border-info-default)',
    'info-subtle': 'var(--color-border-info-subtle)',
    'info-subtlest': 'var(--color-border-info-subtlest)',
    'input-danger': 'var(--color-border-input-danger)',
    input: 'var(--color-border-input-default)',
    'input-disabled': 'var(--color-border-input-disabled)',
    'input-hovered': 'var(--color-border-input-hovered)',
    'input-selected': 'var(--color-border-input-selected)',
    neutral: 'var(--color-border-neutral-default)',
    'neutral-disabled': 'var(--color-border-neutral-disabled)',
    'neutral-hovered': 'var(--color-border-neutral-hovered)',
    'neutral-inverse': 'var(--color-border-neutral-inverse)',
    'neutral-bold': 'var(--color-border-neutral-bold-default)',
    'neutral-bold-hovered': 'var(--color-border-neutral-bold-hovered)',
    'neutral-subtle': 'var(--color-border-neutral-subtle-default)',
    'neutral-subtle-hovered': 'var(--color-border-neutral-subtle-hovered)',
    'neutral-subtlest': 'var(--color-border-neutral-subtlest-default)',
    'neutral-subtlest-hovered': 'var(--color-border-neutral-subtlest-hovered)',
    selected: 'var(--color-border-selected-default)',
    'selected-hover': 'var(--color-border-selected-hover)',
    'selected-subtle': 'var(--color-border-selected-subtle-default)',
    'selected-subtle-hover': 'var(--color-border-selected-subtle-hover)',
    'selected-subtlest': 'var(--color-border-selected-subtlest-default)',
    'selected-subtlest-hover': 'var(--color-border-selected-subtlest-hover)',
    'success-bold': 'var(--color-border-success-bold)',
    success: 'var(--color-border-success-default)',
    'success-subtle': 'var(--color-border-success-subtle)',
    'success-subtlest': 'var(--color-border-success-subtlest)',
    'warning-bold': 'var(--color-border-warning-bold)',
    warning: 'var(--color-border-warning-default)',
    'warning-subtle': 'var(--color-border-warning-subtle)',
    'warning-subtlest': 'var(--color-border-warning-subtlest)',
  },
  textColor: {
    'icon-accent-blue-bold': 'var(--color-icon-accent-blue-bold)',
    'icon-accent-blue': 'var(--color-icon-accent-blue-default)',
    'icon-accent-blue-inverse': 'var(--color-icon-accent-blue-inverse)',
    'icon-accent-blue-subtle': 'var(--color-icon-accent-blue-subtle)',
    'icon-accent-blue-subtlest': 'var(--color-icon-accent-blue-subtlest)',
    'icon-accent-cyan-bold': 'var(--color-icon-accent-cyan-bold)',
    'icon-accent-cyan': 'var(--color-icon-accent-cyan-default)',
    'icon-accent-cyan-inverse': 'var(--color-icon-accent-cyan-inverse)',
    'icon-accent-cyan-subtle': 'var(--color-icon-accent-cyan-subtle)',
    'icon-accent-cyan-subtlest': 'var(--color-icon-accent-cyan-subtlest)',
    'icon-accent-emerald-bold': 'var(--color-icon-accent-emerald-bold)',
    'icon-accent-emerald': 'var(--color-icon-accent-emerald-default)',
    'icon-accent-emerald-inverse': 'var(--color-icon-accent-emerald-inverse)',
    'icon-accent-emerald-subtle': 'var(--color-icon-accent-emerald-subtle)',
    'icon-accent-emerald-subtlest': 'var(--color-icon-accent-emerald-subtlest)',
    'icon-accent-fuchsia-bold': 'var(--color-icon-accent-fuchsia-bold)',
    'icon-accent-fuchsia': 'var(--color-icon-accent-fuchsia-default)',
    'icon-accent-fuchsia-inverse': 'var(--color-icon-accent-fuchsia-inverse)',
    'icon-accent-fuchsia-subtle': 'var(--color-icon-accent-fuchsia-subtle)',
    'icon-accent-fuchsia-subtlest': 'var(--color-icon-accent-fuchsia-subtlest)',
    'icon-accent-green-bold': 'var(--color-icon-accent-green-bold)',
    'icon-accent-green': 'var(--color-icon-accent-green-default)',
    'icon-accent-green-inverse': 'var(--color-icon-accent-green-inverse)',
    'icon-accent-green-subtle': 'var(--color-icon-accent-green-subtle)',
    'icon-accent-green-subtlest': 'var(--color-icon-accent-green-subtlest)',
    'icon-accent-indigo-bold': 'var(--color-icon-accent-indigo-bold)',
    'icon-accent-indigo': 'var(--color-icon-accent-indigo-default)',
    'icon-accent-indigo-inverse': 'var(--color-icon-accent-indigo-inverse)',
    'icon-accent-indigo-subtle': 'var(--color-icon-accent-indigo-subtle)',
    'icon-accent-indigo-subtlest': 'var(--color-icon-accent-indigo-subtlest)',
    'icon-accent-lime-bold': 'var(--color-icon-accent-lime-bold)',
    'icon-accent-lime': 'var(--color-icon-accent-lime-default)',
    'icon-accent-lime-inverse': 'var(--color-icon-accent-lime-inverse)',
    'icon-accent-lime-subtle': 'var(--color-icon-accent-lime-subtle)',
    'icon-accent-lime-subtlest': 'var(--color-icon-accent-lime-subtlest)',
    'icon-accent-orange-bold': 'var(--color-icon-accent-orange-bold)',
    'icon-accent-orange': 'var(--color-icon-accent-orange-default)',
    'icon-accent-orange-inverse': 'var(--color-icon-accent-orange-inverse)',
    'icon-accent-orange-subtle': 'var(--color-icon-accent-orange-subtle)',
    'icon-accent-orange-subtlest': 'var(--color-icon-accent-orange-subtlest)',
    'icon-accent-pink-bold': 'var(--color-icon-accent-pink-bold)',
    'icon-accent-pink': 'var(--color-icon-accent-pink-default)',
    'icon-accent-pink-inverse': 'var(--color-icon-accent-pink-inverse)',
    'icon-accent-pink-subtle': 'var(--color-icon-accent-pink-subtle)',
    'icon-accent-pink-subtlest': 'var(--color-icon-accent-pink-subtlest)',
    'icon-accent-purple-bold': 'var(--color-icon-accent-purple-bold)',
    'icon-accent-purple': 'var(--color-icon-accent-purple-default)',
    'icon-accent-purple-inverse': 'var(--color-icon-accent-purple-inverse)',
    'icon-accent-purple-subtle': 'var(--color-icon-accent-purple-subtle)',
    'icon-accent-purple-subtlest': 'var(--color-icon-accent-purple-subtlest)',
    'icon-accent-red-bold': 'var(--color-icon-accent-red-bold)',
    'icon-accent-red': 'var(--color-icon-accent-red-default)',
    'icon-accent-red-inverse': 'var(--color-icon-accent-red-inverse)',
    'icon-accent-red-subtle': 'var(--color-icon-accent-red-subtle)',
    'icon-accent-red-subtlest': 'var(--color-icon-accent-red-subtlest)',
    'icon-accent-rose-bold': 'var(--color-icon-accent-rose-bold)',
    'icon-accent-rose': 'var(--color-icon-accent-rose-default)',
    'icon-accent-rose-inverse': 'var(--color-icon-accent-rose-inverse)',
    'icon-accent-rose-subtle': 'var(--color-icon-accent-rose-subtle)',
    'icon-accent-rose-subtlest': 'var(--color-icon-accent-rose-subtlest)',
    'icon-accent-rosedust-bold': 'var(--color-icon-accent-rosedust-bold)',
    'icon-accent-rosedust': 'var(--color-icon-accent-rosedust-default)',
    'icon-accent-rosedust-inverse': 'var(--color-icon-accent-rosedust-inverse)',
    'icon-accent-rosedust-subtle': 'var(--color-icon-accent-rosedust-subtle)',
    'icon-accent-rosedust-subtlest': 'var(--color-icon-accent-rosedust-subtlest)',
    'icon-accent-sky-bold': 'var(--color-icon-accent-sky-bold)',
    'icon-accent-sky': 'var(--color-icon-accent-sky-default)',
    'icon-accent-sky-inverse': 'var(--color-icon-accent-sky-inverse)',
    'icon-accent-sky-subtle': 'var(--color-icon-accent-sky-subtle)',
    'icon-accent-sky-subtlest': 'var(--color-icon-accent-sky-subtlest)',
    'icon-accent-teal-bold': 'var(--color-icon-accent-teal-bold)',
    'icon-accent-teal': 'var(--color-icon-accent-teal-default)',
    'icon-accent-teal-inverse': 'var(--color-icon-accent-teal-inverse)',
    'icon-accent-teal-subtle': 'var(--color-icon-accent-teal-subtle)',
    'icon-accent-teal-subtlest': 'var(--color-icon-accent-teal-subtlest)',
    'icon-accent-yellow-bold': 'var(--color-icon-accent-yellow-bold)',
    'icon-accent-yellow': 'var(--color-icon-accent-yellow-default)',
    'icon-accent-yellow-inverse': 'var(--color-icon-accent-yellow-inverse)',
    'icon-accent-yellow-subtle': 'var(--color-icon-accent-yellow-subtle)',
    'icon-accent-yellow-subtlest': 'var(--color-icon-accent-yellow-subtlest)',
    'icon-brand-bold': 'var(--color-icon-brand-bold)',
    'icon-brand': 'var(--color-icon-brand-default)',
    'icon-brand-inverse': 'var(--color-icon-brand-inverse)',
    'icon-brand-subtle': 'var(--color-icon-brand-subtle)',
    'icon-brand-subtlest': 'var(--color-icon-brand-subtlest)',
    'icon-danger-bold': 'var(--color-icon-danger-bold)',
    'icon-danger': 'var(--color-icon-danger-default)',
    'icon-danger-inverse': 'var(--color-icon-danger-inverse)',
    'icon-danger-subtle': 'var(--color-icon-danger-subtle)',
    'icon-danger-subtlest': 'var(--color-icon-danger-subtlest)',
    'icon-discovery-bold': 'var(--color-icon-discovery-bold)',
    'icon-discovery': 'var(--color-icon-discovery-default)',
    'icon-discovery-inverse': 'var(--color-icon-discovery-inverse)',
    'icon-discovery-subtle': 'var(--color-icon-discovery-subtle)',
    'icon-discovery-subtlest': 'var(--color-icon-discovery-subtlest)',
    'icon-info-bold': 'var(--color-icon-info-bold)',
    'icon-info': 'var(--color-icon-info-default)',
    'icon-info-inverse': 'var(--color-icon-info-inverse)',
    'icon-info-subtle': 'var(--color-icon-info-subtle)',
    'icon-info-subtlest': 'var(--color-icon-info-subtlest)',
    'icon-neutral-bold': 'var(--color-icon-neutral-bold)',
    'icon-neutral': 'var(--color-icon-neutral-default)',
    'icon-neutral-inverse': 'var(--color-icon-neutral-inverse)',
    'icon-neutral-subtle': 'var(--color-icon-neutral-subtle)',
    'icon-neutral-subtlest': 'var(--color-icon-neutral-subtlest)',
    'icon-selected-bold': 'var(--color-icon-selected-bold)',
    'icon-selected': 'var(--color-icon-selected-default)',
    'icon-selected-inverse': 'var(--color-icon-selected-inverse)',
    'icon-selected-subtle': 'var(--color-icon-selected-subtle)',
    'icon-selected-subtlest': 'var(--color-icon-selected-subtlest)',
    'icon-success-bold': 'var(--color-icon-success-bold)',
    'icon-success': 'var(--color-icon-success-default)',
    'icon-success-inverse': 'var(--color-icon-success-inverse)',
    'icon-success-subtle': 'var(--color-icon-success-subtle)',
    'icon-success-subtlest': 'var(--color-icon-success-subtlest)',
    'icon-warning-bold': 'var(--color-icon-warning-bold)',
    'icon-warning': 'var(--color-icon-warning-default)',
    'icon-warning-inverse': 'var(--color-icon-warning-inverse)',
    'icon-warning-subtle': 'var(--color-icon-warning-subtle)',
    'icon-warning-subtlest': 'var(--color-icon-warning-subtlest)',
    'interaction-hovered': 'var(--color-interaction-hovered)',
    'interaction-pressed': 'var(--color-interaction-pressed)',
    'link-brand': 'var(--color-link-brand-default)',
    'link-brand-hovered': 'var(--color-link-brand-hovered)',
    'link-brand-pressed': 'var(--color-link-brand-pressed)',
    'link-danger': 'var(--color-link-danger-default)',
    'link-danger-hovered': 'var(--color-link-danger-hovered)',
    'link-danger-pressed': 'var(--color-link-danger-pressed)',
    'link-discovery': 'var(--color-link-discovery-default)',
    'link-discovery-hovered': 'var(--color-link-discovery-hovered)',
    'link-discovery-pressed': 'var(--color-link-discovery-pressed)',
    'link-info': 'var(--color-link-info-default)',
    'link-info-hovered': 'var(--color-link-info-hovered)',
    'link-info-pressed': 'var(--color-link-info-pressed)',
    'link-inverse': 'var(--color-link-inverse-default)',
    'link-inverse-hovered': 'var(--color-link-inverse-hovered)',
    'link-inverse-pressed': 'var(--color-link-inverse-pressed)',
    'link-neutral': 'var(--color-link-neutral-default)',
    'link-neutral-hovered': 'var(--color-link-neutral-hovered)',
    'link-neutral-pressed': 'var(--color-link-neutral-pressed)',
    'link-success': 'var(--color-link-success-default)',
    'link-success-hovered': 'var(--color-link-success-hovered)',
    'link-success-pressed': 'var(--color-link-success-pressed)',
    'link-warning': 'var(--color-link-warning-default)',
    'link-warning-hovered': 'var(--color-link-warning-hovered)',
    'link-warning-pressed': 'var(--color-link-warning-pressed)',
    'accent-blue': 'var(--color-text-accent-blue-default)',
    'accent-blue-inverse': 'var(--color-text-accent-blue-inverse)',
    'accent-blue-subtle': 'var(--color-text-accent-blue-subtle)',
    'accent-cyan': 'var(--color-text-accent-cyan-default)',
    'accent-cyan-inverse': 'var(--color-text-accent-cyan-inverse)',
    'accent-cyan-subtle': 'var(--color-text-accent-cyan-subtle)',
    'accent-emerald': 'var(--color-text-accent-emerald-default)',
    'accent-emerald-inverse': 'var(--color-text-accent-emerald-inverse)',
    'accent-emerald-subtle': 'var(--color-text-accent-emerald-subtle)',
    'accent-fuchsia': 'var(--color-text-accent-fuchsia-default)',
    'accent-fuchsia-inverse': 'var(--color-text-accent-fuchsia-inverse)',
    'accent-fuchsia-subtle': 'var(--color-text-accent-fuchsia-subtle)',
    'accent-green': 'var(--color-text-accent-green-default)',
    'accent-green-inverse': 'var(--color-text-accent-green-inverse)',
    'accent-green-subtle': 'var(--color-text-accent-green-subtle)',
    'accent-indigo': 'var(--color-text-accent-indigo-default)',
    'accent-indigo-inverse': 'var(--color-text-accent-indigo-inverse)',
    'accent-indigo-subtle': 'var(--color-text-accent-indigo-subtle)',
    'accent-lime': 'var(--color-text-accent-lime-default)',
    'accent-lime-inverse': 'var(--color-text-accent-lime-inverse)',
    'accent-lime-subtle': 'var(--color-text-accent-lime-subtle)',
    'accent-orange': 'var(--color-text-accent-orange-default)',
    'accent-orange-inverse': 'var(--color-text-accent-orange-inverse)',
    'accent-orange-subtle': 'var(--color-text-accent-orange-subtle)',
    'accent-pink': 'var(--color-text-accent-pink-default)',
    'accent-pink-inverse': 'var(--color-text-accent-pink-inverse)',
    'accent-pink-subtle': 'var(--color-text-accent-pink-subtle)',
    'accent-purple': 'var(--color-text-accent-purple-default)',
    'accent-purple-inverse': 'var(--color-text-accent-purple-inverse)',
    'accent-purple-subtle': 'var(--color-text-accent-purple-subtle)',
    'accent-red': 'var(--color-text-accent-red-default)',
    'accent-red-inverse': 'var(--color-text-accent-red-inverse)',
    'accent-red-subtle': 'var(--color-text-accent-red-subtle)',
    'accent-rose': 'var(--color-text-accent-rose-default)',
    'accent-rose-inverse': 'var(--color-text-accent-rose-inverse)',
    'accent-rose-subtle': 'var(--color-text-accent-rose-subtle)',
    'accent-rosedust': 'var(--color-text-accent-rosedust-default)',
    'accent-rosedust-inverse': 'var(--color-text-accent-rosedust-inverse)',
    'accent-rosedust-subtle': 'var(--color-text-accent-rosedust-subtle)',
    'accent-sky': 'var(--color-text-accent-sky-default)',
    'accent-sky-inverse': 'var(--color-text-accent-sky-inverse)',
    'accent-sky-subtle': 'var(--color-text-accent-sky-subtle)',
    'accent-teal': 'var(--color-text-accent-teal-default)',
    'accent-teal-inverse': 'var(--color-text-accent-teal-inverse)',
    'accent-teal-subtle': 'var(--color-text-accent-teal-subtle)',
    'accent-yellow': 'var(--color-text-accent-yellow-default)',
    'accent-yellow-inverse': 'var(--color-text-accent-yellow-inverse)',
    'accent-yellow-subtle': 'var(--color-text-accent-yellow-subtle)',
    'brand-bold': 'var(--color-text-brand-bold)',
    brand: 'var(--color-text-brand-default)',
    'brand-inverse': 'var(--color-text-brand-inverse)',
    'brand-subtle': 'var(--color-text-brand-subtle)',
    'brand-subtlest': 'var(--color-text-brand-subtlest)',
    danger: 'var(--color-text-danger-default)',
    'danger-inverse': 'var(--color-text-danger-inverse)',
    'danger-subtle': 'var(--color-text-danger-subtle)',
    discovery: 'var(--color-text-discovery-default)',
    'discovery-inverse': 'var(--color-text-discovery-inverse)',
    'discovery-subtle': 'var(--color-text-discovery-subtle)',
    info: 'var(--color-text-info-default)',
    'info-inverse': 'var(--color-text-info-inverse)',
    'info-subtle': 'var(--color-text-info-subtle)',
    'neutral-bold': 'var(--color-text-neutral-bold)',
    neutral: 'var(--color-text-neutral-default)',
    'neutral-disabled': 'var(--color-text-neutral-disabled)',
    'neutral-inverse': 'var(--color-text-neutral-inverse)',
    'neutral-subtle': 'var(--color-text-neutral-subtle)',
    'neutral-subtlest': 'var(--color-text-neutral-subtlest)',
    'selected-bold': 'var(--color-text-selected-bold)',
    selected: 'var(--color-text-selected-default)',
    'selected-inverse': 'var(--color-text-selected-inverse)',
    'selected-subtle': 'var(--color-text-selected-subtle)',
    'selected-subtlest': 'var(--color-text-selected-subtlest)',
    success: 'var(--color-text-success-default)',
    'success-inverse': 'var(--color-text-success-inverse)',
    'success-subtle': 'var(--color-text-success-subtle)',
    warning: 'var(--color-text-warning-default)',
    'warning-inverse': 'var(--color-text-warning-inverse)',
    'warning-subtle': 'var(--color-text-warning-subtle)',
  },
  backgroundColor: {
    'accent-blue': 'var(--color-background-accent-blue-default)',
    'accent-blue-hovered': 'var(--color-background-accent-blue-hovered)',
    'accent-blue-pressed': 'var(--color-background-accent-blue-pressed)',
    'accent-blue-alpha': 'var(--color-background-accent-blue-alpha-default)',
    'accent-blue-alpha-hovered': 'var(--color-background-accent-blue-alpha-hovered)',
    'accent-blue-alpha-pressed': 'var(--color-background-accent-blue-alpha-pressed)',
    'accent-blue-bold': 'var(--color-background-accent-blue-bold-default)',
    'accent-blue-bold-hovered': 'var(--color-background-accent-blue-bold-hovered)',
    'accent-blue-bold-pressed': 'var(--color-background-accent-blue-bold-pressed)',
    'accent-blue-bolder': 'var(--color-background-accent-blue-bolder-default)',
    'accent-blue-bolder-hovered': 'var(--color-background-accent-blue-bolder-hovered)',
    'accent-blue-bolder-pressed': 'var(--color-background-accent-blue-bolder-pressed)',
    'accent-blue-subtle': 'var(--color-background-accent-blue-subtle-default)',
    'accent-blue-subtle-hovered': 'var(--color-background-accent-blue-subtle-hovered)',
    'accent-blue-subtle-pressed': 'var(--color-background-accent-blue-subtle-pressed)',
    'accent-blue-subtlest': 'var(--color-background-accent-blue-subtlest-default)',
    'accent-blue-subtlest-hovered': 'var(--color-background-accent-blue-subtlest-hovered)',
    'accent-blue-subtlest-pressed': 'var(--color-background-accent-blue-subtlest-pressed)',
    'accent-blue-white': 'var(--color-background-accent-blue-white-default)',
    'accent-blue-white-hovered': 'var(--color-background-accent-blue-white-hovered)',
    'accent-blue-white-pressed': 'var(--color-background-accent-blue-white-pressed)',
    'accent-cyan': 'var(--color-background-accent-cyan-default)',
    'accent-cyan-hovered': 'var(--color-background-accent-cyan-hovered)',
    'accent-cyan-pressed': 'var(--color-background-accent-cyan-pressed)',
    'accent-cyan-alpha': 'var(--color-background-accent-cyan-alpha-default)',
    'accent-cyan-alpha-hovered': 'var(--color-background-accent-cyan-alpha-hovered)',
    'accent-cyan-alpha-pressed': 'var(--color-background-accent-cyan-alpha-pressed)',
    'accent-cyan-bold': 'var(--color-background-accent-cyan-bold-default)',
    'accent-cyan-bold-hovered': 'var(--color-background-accent-cyan-bold-hovered)',
    'accent-cyan-bold-pressed': 'var(--color-background-accent-cyan-bold-pressed)',
    'accent-cyan-bolder': 'var(--color-background-accent-cyan-bolder-default)',
    'accent-cyan-bolder-hovered': 'var(--color-background-accent-cyan-bolder-hovered)',
    'accent-cyan-bolder-pressed': 'var(--color-background-accent-cyan-bolder-pressed)',
    'accent-cyan-subtle': 'var(--color-background-accent-cyan-subtle-default)',
    'accent-cyan-subtle-hovered': 'var(--color-background-accent-cyan-subtle-hovered)',
    'accent-cyan-subtle-pressed': 'var(--color-background-accent-cyan-subtle-pressed)',
    'accent-cyan-subtlest': 'var(--color-background-accent-cyan-subtlest-default)',
    'accent-cyan-subtlest-hovered': 'var(--color-background-accent-cyan-subtlest-hovered)',
    'accent-cyan-subtlest-pressed': 'var(--color-background-accent-cyan-subtlest-pressed)',
    'accent-cyan-white': 'var(--color-background-accent-cyan-white-default)',
    'accent-cyan-white-hovered': 'var(--color-background-accent-cyan-white-hovered)',
    'accent-cyan-white-pressed': 'var(--color-background-accent-cyan-white-pressed)',
    'accent-emerald': 'var(--color-background-accent-emerald-default)',
    'accent-emerald-hovered': 'var(--color-background-accent-emerald-hovered)',
    'accent-emerald-pressed': 'var(--color-background-accent-emerald-pressed)',
    'accent-emerald-alpha': 'var(--color-background-accent-emerald-alpha-default)',
    'accent-emerald-alpha-hovered': 'var(--color-background-accent-emerald-alpha-hovered)',
    'accent-emerald-alpha-pressed': 'var(--color-background-accent-emerald-alpha-pressed)',
    'accent-emerald-bold': 'var(--color-background-accent-emerald-bold-default)',
    'accent-emerald-bold-hovered': 'var(--color-background-accent-emerald-bold-hovered)',
    'accent-emerald-bold-pressed': 'var(--color-background-accent-emerald-bold-pressed)',
    'accent-emerald-bolder': 'var(--color-background-accent-emerald-bolder-default)',
    'accent-emerald-bolder-hovered': 'var(--color-background-accent-emerald-bolder-hovered)',
    'accent-emerald-bolder-pressed': 'var(--color-background-accent-emerald-bolder-pressed)',
    'accent-emerald-subtle': 'var(--color-background-accent-emerald-subtle-default)',
    'accent-emerald-subtle-hovered': 'var(--color-background-accent-emerald-subtle-hovered)',
    'accent-emerald-subtle-pressed': 'var(--color-background-accent-emerald-subtle-pressed)',
    'accent-emerald-subtlest': 'var(--color-background-accent-emerald-subtlest-default)',
    'accent-emerald-subtlest-hovered': 'var(--color-background-accent-emerald-subtlest-hovered)',
    'accent-emerald-subtlest-pressed': 'var(--color-background-accent-emerald-subtlest-pressed)',
    'accent-emerald-white': 'var(--color-background-accent-emerald-white-default)',
    'accent-emerald-white-hovered': 'var(--color-background-accent-emerald-white-hovered)',
    'accent-emerald-white-pressed': 'var(--color-background-accent-emerald-white-pressed)',
    'accent-fuchsia': 'var(--color-background-accent-fuchsia-default)',
    'accent-fuchsia-hovered': 'var(--color-background-accent-fuchsia-hovered)',
    'accent-fuchsia-pressed': 'var(--color-background-accent-fuchsia-pressed)',
    'accent-fuchsia-alpha': 'var(--color-background-accent-fuchsia-alpha-default)',
    'accent-fuchsia-alpha-hovered': 'var(--color-background-accent-fuchsia-alpha-hovered)',
    'accent-fuchsia-alpha-pressed': 'var(--color-background-accent-fuchsia-alpha-pressed)',
    'accent-fuchsia-bold': 'var(--color-background-accent-fuchsia-bold-default)',
    'accent-fuchsia-bold-hovered': 'var(--color-background-accent-fuchsia-bold-hovered)',
    'accent-fuchsia-bold-pressed': 'var(--color-background-accent-fuchsia-bold-pressed)',
    'accent-fuchsia-bolder': 'var(--color-background-accent-fuchsia-bolder-default)',
    'accent-fuchsia-bolder-hovered': 'var(--color-background-accent-fuchsia-bolder-hovered)',
    'accent-fuchsia-bolder-pressed': 'var(--color-background-accent-fuchsia-bolder-pressed)',
    'accent-fuchsia-subtle': 'var(--color-background-accent-fuchsia-subtle-default)',
    'accent-fuchsia-subtle-hovered': 'var(--color-background-accent-fuchsia-subtle-hovered)',
    'accent-fuchsia-subtle-pressed': 'var(--color-background-accent-fuchsia-subtle-pressed)',
    'accent-fuchsia-subtlest': 'var(--color-background-accent-fuchsia-subtlest-default)',
    'accent-fuchsia-subtlest-hovered': 'var(--color-background-accent-fuchsia-subtlest-hovered)',
    'accent-fuchsia-subtlest-pressed': 'var(--color-background-accent-fuchsia-subtlest-pressed)',
    'accent-fuchsia-white': 'var(--color-background-accent-fuchsia-white-default)',
    'accent-fuchsia-white-hovered': 'var(--color-background-accent-fuchsia-white-hovered)',
    'accent-fuchsia-white-pressed': 'var(--color-background-accent-fuchsia-white-pressed)',
    'accent-green': 'var(--color-background-accent-green-default)',
    'accent-green-hovered': 'var(--color-background-accent-green-hovered)',
    'accent-green-pressed': 'var(--color-background-accent-green-pressed)',
    'accent-green-alpha': 'var(--color-background-accent-green-alpha-default)',
    'accent-green-alpha-hovered': 'var(--color-background-accent-green-alpha-hovered)',
    'accent-green-alpha-pressed': 'var(--color-background-accent-green-alpha-pressed)',
    'accent-green-bold': 'var(--color-background-accent-green-bold-default)',
    'accent-green-bold-hovered': 'var(--color-background-accent-green-bold-hovered)',
    'accent-green-bold-pressed': 'var(--color-background-accent-green-bold-pressed)',
    'accent-green-bolder': 'var(--color-background-accent-green-bolder-default)',
    'accent-green-bolder-hovered': 'var(--color-background-accent-green-bolder-hovered)',
    'accent-green-bolder-pressed': 'var(--color-background-accent-green-bolder-pressed)',
    'accent-green-subtle': 'var(--color-background-accent-green-subtle-default)',
    'accent-green-subtle-hovered': 'var(--color-background-accent-green-subtle-hovered)',
    'accent-green-subtle-pressed': 'var(--color-background-accent-green-subtle-pressed)',
    'accent-green-subtlest': 'var(--color-background-accent-green-subtlest-default)',
    'accent-green-subtlest-hovered': 'var(--color-background-accent-green-subtlest-hovered)',
    'accent-green-subtlest-pressed': 'var(--color-background-accent-green-subtlest-pressed)',
    'accent-green-white': 'var(--color-background-accent-green-white-default)',
    'accent-green-white-hovered': 'var(--color-background-accent-green-white-hovered)',
    'accent-green-white-pressed': 'var(--color-background-accent-green-white-pressed)',
    'accent-indigo': 'var(--color-background-accent-indigo-default)',
    'accent-indigo-hovered': 'var(--color-background-accent-indigo-hovered)',
    'accent-indigo-pressed': 'var(--color-background-accent-indigo-pressed)',
    'accent-indigo-alpha': 'var(--color-background-accent-indigo-alpha-default)',
    'accent-indigo-alpha-hovered': 'var(--color-background-accent-indigo-alpha-hovered)',
    'accent-indigo-alpha-pressed': 'var(--color-background-accent-indigo-alpha-pressed)',
    'accent-indigo-bold': 'var(--color-background-accent-indigo-bold-default)',
    'accent-indigo-bold-hovered': 'var(--color-background-accent-indigo-bold-hovered)',
    'accent-indigo-bold-pressed': 'var(--color-background-accent-indigo-bold-pressed)',
    'accent-indigo-bolder': 'var(--color-background-accent-indigo-bolder-default)',
    'accent-indigo-bolder-hovered': 'var(--color-background-accent-indigo-bolder-hovered)',
    'accent-indigo-bolder-pressed': 'var(--color-background-accent-indigo-bolder-pressed)',
    'accent-indigo-subtle': 'var(--color-background-accent-indigo-subtle-default)',
    'accent-indigo-subtle-hovered': 'var(--color-background-accent-indigo-subtle-hovered)',
    'accent-indigo-subtle-pressed': 'var(--color-background-accent-indigo-subtle-pressed)',
    'accent-indigo-subtlest': 'var(--color-background-accent-indigo-subtlest-default)',
    'accent-indigo-subtlest-hovered': 'var(--color-background-accent-indigo-subtlest-hovered)',
    'accent-indigo-subtlest-pressed': 'var(--color-background-accent-indigo-subtlest-pressed)',
    'accent-indigo-white': 'var(--color-background-accent-indigo-white-default)',
    'accent-indigo-white-hovered': 'var(--color-background-accent-indigo-white-hovered)',
    'accent-indigo-white-pressed': 'var(--color-background-accent-indigo-white-pressed)',
    'accent-lime': 'var(--color-background-accent-lime-default)',
    'accent-lime-hovered': 'var(--color-background-accent-lime-hovered)',
    'accent-lime-pressed': 'var(--color-background-accent-lime-pressed)',
    'accent-lime-alpha': 'var(--color-background-accent-lime-alpha-default)',
    'accent-lime-alpha-hovered': 'var(--color-background-accent-lime-alpha-hovered)',
    'accent-lime-alpha-pressed': 'var(--color-background-accent-lime-alpha-pressed)',
    'accent-lime-bold': 'var(--color-background-accent-lime-bold-default)',
    'accent-lime-bold-hovered': 'var(--color-background-accent-lime-bold-hovered)',
    'accent-lime-bold-pressed': 'var(--color-background-accent-lime-bold-pressed)',
    'accent-lime-bolder': 'var(--color-background-accent-lime-bolder-default)',
    'accent-lime-bolder-hovered': 'var(--color-background-accent-lime-bolder-hovered)',
    'accent-lime-bolder-pressed': 'var(--color-background-accent-lime-bolder-pressed)',
    'accent-lime-subtle': 'var(--color-background-accent-lime-subtle-default)',
    'accent-lime-subtle-hovered': 'var(--color-background-accent-lime-subtle-hovered)',
    'accent-lime-subtle-pressed': 'var(--color-background-accent-lime-subtle-pressed)',
    'accent-lime-subtlest': 'var(--color-background-accent-lime-subtlest-default)',
    'accent-lime-subtlest-hovered': 'var(--color-background-accent-lime-subtlest-hovered)',
    'accent-lime-subtlest-pressed': 'var(--color-background-accent-lime-subtlest-pressed)',
    'accent-lime-white': 'var(--color-background-accent-lime-white-default)',
    'accent-lime-white-hovered': 'var(--color-background-accent-lime-white-hovered)',
    'accent-lime-white-pressed': 'var(--color-background-accent-lime-white-pressed)',
    'accent-orange': 'var(--color-background-accent-orange-default)',
    'accent-orange-hovered': 'var(--color-background-accent-orange-hovered)',
    'accent-orange-pressed': 'var(--color-background-accent-orange-pressed)',
    'accent-orange-alpha': 'var(--color-background-accent-orange-alpha-default)',
    'accent-orange-alpha-hovered': 'var(--color-background-accent-orange-alpha-hovered)',
    'accent-orange-alpha-pressed': 'var(--color-background-accent-orange-alpha-pressed)',
    'accent-orange-bold': 'var(--color-background-accent-orange-bold-default)',
    'accent-orange-bold-hovered': 'var(--color-background-accent-orange-bold-hovered)',
    'accent-orange-bold-pressed': 'var(--color-background-accent-orange-bold-pressed)',
    'accent-orange-bolder': 'var(--color-background-accent-orange-bolder-default)',
    'accent-orange-bolder-hovered': 'var(--color-background-accent-orange-bolder-hovered)',
    'accent-orange-bolder-pressed': 'var(--color-background-accent-orange-bolder-pressed)',
    'accent-orange-subtle': 'var(--color-background-accent-orange-subtle-default)',
    'accent-orange-subtle-hovered': 'var(--color-background-accent-orange-subtle-hovered)',
    'accent-orange-subtle-pressed': 'var(--color-background-accent-orange-subtle-pressed)',
    'accent-orange-subtlest': 'var(--color-background-accent-orange-subtlest-default)',
    'accent-orange-subtlest-hovered': 'var(--color-background-accent-orange-subtlest-hovered)',
    'accent-orange-subtlest-pressed': 'var(--color-background-accent-orange-subtlest-pressed)',
    'accent-orange-white': 'var(--color-background-accent-orange-white-default)',
    'accent-orange-white-hovered': 'var(--color-background-accent-orange-white-hovered)',
    'accent-orange-white-pressed': 'var(--color-background-accent-orange-white-pressed)',
    'accent-pink': 'var(--color-background-accent-pink-default)',
    'accent-pink-hovered': 'var(--color-background-accent-pink-hovered)',
    'accent-pink-pressed': 'var(--color-background-accent-pink-pressed)',
    'accent-pink-alpha': 'var(--color-background-accent-pink-alpha-default)',
    'accent-pink-alpha-hovered': 'var(--color-background-accent-pink-alpha-hovered)',
    'accent-pink-alpha-pressed': 'var(--color-background-accent-pink-alpha-pressed)',
    'accent-pink-bold': 'var(--color-background-accent-pink-bold-default)',
    'accent-pink-bold-hovered': 'var(--color-background-accent-pink-bold-hovered)',
    'accent-pink-bold-pressed': 'var(--color-background-accent-pink-bold-pressed)',
    'accent-pink-bolder': 'var(--color-background-accent-pink-bolder-default)',
    'accent-pink-bolder-hovered': 'var(--color-background-accent-pink-bolder-hovered)',
    'accent-pink-bolder-pressed': 'var(--color-background-accent-pink-bolder-pressed)',
    'accent-pink-subtle': 'var(--color-background-accent-pink-subtle-default)',
    'accent-pink-subtle-hovered': 'var(--color-background-accent-pink-subtle-hovered)',
    'accent-pink-subtle-pressed': 'var(--color-background-accent-pink-subtle-pressed)',
    'accent-pink-subtlest': 'var(--color-background-accent-pink-subtlest-default)',
    'accent-pink-subtlest-hovered': 'var(--color-background-accent-pink-subtlest-hovered)',
    'accent-pink-subtlest-pressed': 'var(--color-background-accent-pink-subtlest-pressed)',
    'accent-pink-white': 'var(--color-background-accent-pink-white-default)',
    'accent-pink-white-hovered': 'var(--color-background-accent-pink-white-hovered)',
    'accent-pink-white-pressed': 'var(--color-background-accent-pink-white-pressed)',
    'accent-purple': 'var(--color-background-accent-purple-default)',
    'accent-purple-hovered': 'var(--color-background-accent-purple-hovered)',
    'accent-purple-pressed': 'var(--color-background-accent-purple-pressed)',
    'accent-purple-alpha': 'var(--color-background-accent-purple-alpha-default)',
    'accent-purple-alpha-hovered': 'var(--color-background-accent-purple-alpha-hovered)',
    'accent-purple-alpha-pressed': 'var(--color-background-accent-purple-alpha-pressed)',
    'accent-purple-bold': 'var(--color-background-accent-purple-bold-default)',
    'accent-purple-bold-hovered': 'var(--color-background-accent-purple-bold-hovered)',
    'accent-purple-bold-pressed': 'var(--color-background-accent-purple-bold-pressed)',
    'accent-purple-bolder': 'var(--color-background-accent-purple-bolder-default)',
    'accent-purple-bolder-hovered': 'var(--color-background-accent-purple-bolder-hovered)',
    'accent-purple-bolder-pressed': 'var(--color-background-accent-purple-bolder-pressed)',
    'accent-purple-subtle': 'var(--color-background-accent-purple-subtle-default)',
    'accent-purple-subtle-hovered': 'var(--color-background-accent-purple-subtle-hovered)',
    'accent-purple-subtle-pressed': 'var(--color-background-accent-purple-subtle-pressed)',
    'accent-purple-subtlest': 'var(--color-background-accent-purple-subtlest-default)',
    'accent-purple-subtlest-hovered': 'var(--color-background-accent-purple-subtlest-hovered)',
    'accent-purple-subtlest-pressed': 'var(--color-background-accent-purple-subtlest-pressed)',
    'accent-purple-white': 'var(--color-background-accent-purple-white-default)',
    'accent-purple-white-hovered': 'var(--color-background-accent-purple-white-hovered)',
    'accent-purple-white-pressed': 'var(--color-background-accent-purple-white-pressed)',
    'accent-red': 'var(--color-background-accent-red-default)',
    'accent-red-hovered': 'var(--color-background-accent-red-hovered)',
    'accent-red-pressed': 'var(--color-background-accent-red-pressed)',
    'accent-red-alpha': 'var(--color-background-accent-red-alpha-default)',
    'accent-red-alpha-hovered': 'var(--color-background-accent-red-alpha-hovered)',
    'accent-red-alpha-pressed': 'var(--color-background-accent-red-alpha-pressed)',
    'accent-red-bold': 'var(--color-background-accent-red-bold-default)',
    'accent-red-bold-hovered': 'var(--color-background-accent-red-bold-hovered)',
    'accent-red-bold-pressed': 'var(--color-background-accent-red-bold-pressed)',
    'accent-red-bolder': 'var(--color-background-accent-red-bolder-default)',
    'accent-red-bolder-hovered': 'var(--color-background-accent-red-bolder-hovered)',
    'accent-red-bolder-pressed': 'var(--color-background-accent-red-bolder-pressed)',
    'accent-red-subtle': 'var(--color-background-accent-red-subtle-default)',
    'accent-red-subtle-hovered': 'var(--color-background-accent-red-subtle-hovered)',
    'accent-red-subtle-pressed': 'var(--color-background-accent-red-subtle-pressed)',
    'accent-red-subtlest': 'var(--color-background-accent-red-subtlest-default)',
    'accent-red-subtlest-hovered': 'var(--color-background-accent-red-subtlest-hovered)',
    'accent-red-subtlest-pressed': 'var(--color-background-accent-red-subtlest-pressed)',
    'accent-red-white': 'var(--color-background-accent-red-white-default)',
    'accent-red-white-hovered': 'var(--color-background-accent-red-white-hovered)',
    'accent-red-white-pressed': 'var(--color-background-accent-red-white-pressed)',
    'accent-rose': 'var(--color-background-accent-rose-default)',
    'accent-rose-hovered': 'var(--color-background-accent-rose-hovered)',
    'accent-rose-pressed': 'var(--color-background-accent-rose-pressed)',
    'accent-rose-alpha': 'var(--color-background-accent-rose-alpha-default)',
    'accent-rose-alpha-hovered': 'var(--color-background-accent-rose-alpha-hovered)',
    'accent-rose-alpha-pressed': 'var(--color-background-accent-rose-alpha-pressed)',
    'accent-rose-bold': 'var(--color-background-accent-rose-bold-default)',
    'accent-rose-bold-hovered': 'var(--color-background-accent-rose-bold-hovered)',
    'accent-rose-bold-pressed': 'var(--color-background-accent-rose-bold-pressed)',
    'accent-rose-bolder': 'var(--color-background-accent-rose-bolder-default)',
    'accent-rose-bolder-hovered': 'var(--color-background-accent-rose-bolder-hovered)',
    'accent-rose-bolder-pressed': 'var(--color-background-accent-rose-bolder-pressed)',
    'accent-rose-subtle': 'var(--color-background-accent-rose-subtle-default)',
    'accent-rose-subtle-hovered': 'var(--color-background-accent-rose-subtle-hovered)',
    'accent-rose-subtle-pressed': 'var(--color-background-accent-rose-subtle-pressed)',
    'accent-rose-subtlest': 'var(--color-background-accent-rose-subtlest-default)',
    'accent-rose-subtlest-hovered': 'var(--color-background-accent-rose-subtlest-hovered)',
    'accent-rose-subtlest-pressed': 'var(--color-background-accent-rose-subtlest-pressed)',
    'accent-rose-white': 'var(--color-background-accent-rose-white-default)',
    'accent-rose-white-hovered': 'var(--color-background-accent-rose-white-hovered)',
    'accent-rose-white-pressed': 'var(--color-background-accent-rose-white-pressed)',
    'accent-rosedust': 'var(--color-background-accent-rosedust-default)',
    'accent-rosedust-hovered': 'var(--color-background-accent-rosedust-hovered)',
    'accent-rosedust-pressed': 'var(--color-background-accent-rosedust-pressed)',
    'accent-rosedust-alpha': 'var(--color-background-accent-rosedust-alpha-default)',
    'accent-rosedust-alpha-hovered': 'var(--color-background-accent-rosedust-alpha-hovered)',
    'accent-rosedust-alpha-pressed': 'var(--color-background-accent-rosedust-alpha-pressed)',
    'accent-rosedust-bold': 'var(--color-background-accent-rosedust-bold-default)',
    'accent-rosedust-bold-hovered': 'var(--color-background-accent-rosedust-bold-hovered)',
    'accent-rosedust-bold-pressed': 'var(--color-background-accent-rosedust-bold-pressed)',
    'accent-rosedust-bolder': 'var(--color-background-accent-rosedust-bolder-default)',
    'accent-rosedust-bolder-hovered': 'var(--color-background-accent-rosedust-bolder-hovered)',
    'accent-rosedust-bolder-pressed': 'var(--color-background-accent-rosedust-bolder-pressed)',
    'accent-rosedust-subtle': 'var(--color-background-accent-rosedust-subtle-default)',
    'accent-rosedust-subtle-hovered': 'var(--color-background-accent-rosedust-subtle-hovered)',
    'accent-rosedust-subtle-pressed': 'var(--color-background-accent-rosedust-subtle-pressed)',
    'accent-rosedust-subtlest': 'var(--color-background-accent-rosedust-subtlest-default)',
    'accent-rosedust-subtlest-hovered': 'var(--color-background-accent-rosedust-subtlest-hovered)',
    'accent-rosedust-subtlest-pressed': 'var(--color-background-accent-rosedust-subtlest-pressed)',
    'accent-rosedust-white': 'var(--color-background-accent-rosedust-white-default)',
    'accent-rosedust-white-hovered': 'var(--color-background-accent-rosedust-white-hovered)',
    'accent-rosedust-white-pressed': 'var(--color-background-accent-rosedust-white-pressed)',
    'accent-sky': 'var(--color-background-accent-sky-default)',
    'accent-sky-hovered': 'var(--color-background-accent-sky-hovered)',
    'accent-sky-pressed': 'var(--color-background-accent-sky-pressed)',
    'accent-sky-alpha': 'var(--color-background-accent-sky-alpha-default)',
    'accent-sky-alpha-hovered': 'var(--color-background-accent-sky-alpha-hovered)',
    'accent-sky-alpha-pressed': 'var(--color-background-accent-sky-alpha-pressed)',
    'accent-sky-bold': 'var(--color-background-accent-sky-bold-default)',
    'accent-sky-bold-hovered': 'var(--color-background-accent-sky-bold-hovered)',
    'accent-sky-bold-pressed': 'var(--color-background-accent-sky-bold-pressed)',
    'accent-sky-bolder': 'var(--color-background-accent-sky-bolder-default)',
    'accent-sky-bolder-hovered': 'var(--color-background-accent-sky-bolder-hovered)',
    'accent-sky-bolder-pressed': 'var(--color-background-accent-sky-bolder-pressed)',
    'accent-sky-subtle': 'var(--color-background-accent-sky-subtle-default)',
    'accent-sky-subtle-hovered': 'var(--color-background-accent-sky-subtle-hovered)',
    'accent-sky-subtle-pressed': 'var(--color-background-accent-sky-subtle-pressed)',
    'accent-sky-subtlest': 'var(--color-background-accent-sky-subtlest-default)',
    'accent-sky-subtlest-hovered': 'var(--color-background-accent-sky-subtlest-hovered)',
    'accent-sky-subtlest-pressed': 'var(--color-background-accent-sky-subtlest-pressed)',
    'accent-sky-white': 'var(--color-background-accent-sky-white-default)',
    'accent-sky-white-hovered': 'var(--color-background-accent-sky-white-hovered)',
    'accent-sky-white-pressed': 'var(--color-background-accent-sky-white-pressed)',
    'accent-teal': 'var(--color-background-accent-teal-default)',
    'accent-teal-hovered': 'var(--color-background-accent-teal-hovered)',
    'accent-teal-pressed': 'var(--color-background-accent-teal-pressed)',
    'accent-teal-alpha': 'var(--color-background-accent-teal-alpha-default)',
    'accent-teal-alpha-hovered': 'var(--color-background-accent-teal-alpha-hovered)',
    'accent-teal-alpha-pressed': 'var(--color-background-accent-teal-alpha-pressed)',
    'accent-teal-bold': 'var(--color-background-accent-teal-bold-default)',
    'accent-teal-bold-hovered': 'var(--color-background-accent-teal-bold-hovered)',
    'accent-teal-bold-pressed': 'var(--color-background-accent-teal-bold-pressed)',
    'accent-teal-bolder': 'var(--color-background-accent-teal-bolder-default)',
    'accent-teal-bolder-hovered': 'var(--color-background-accent-teal-bolder-hovered)',
    'accent-teal-bolder-pressed': 'var(--color-background-accent-teal-bolder-pressed)',
    'accent-teal-subtle': 'var(--color-background-accent-teal-subtle-default)',
    'accent-teal-subtle-hovered': 'var(--color-background-accent-teal-subtle-hovered)',
    'accent-teal-subtle-pressed': 'var(--color-background-accent-teal-subtle-pressed)',
    'accent-teal-subtlest': 'var(--color-background-accent-teal-subtlest-default)',
    'accent-teal-subtlest-hovered': 'var(--color-background-accent-teal-subtlest-hovered)',
    'accent-teal-subtlest-pressed': 'var(--color-background-accent-teal-subtlest-pressed)',
    'accent-teal-white': 'var(--color-background-accent-teal-white-default)',
    'accent-teal-white-hovered': 'var(--color-background-accent-teal-white-hovered)',
    'accent-teal-white-pressed': 'var(--color-background-accent-teal-white-pressed)',
    'accent-yellow': 'var(--color-background-accent-yellow-default)',
    'accent-yellow-hovered': 'var(--color-background-accent-yellow-hovered)',
    'accent-yellow-pressed': 'var(--color-background-accent-yellow-pressed)',
    'accent-yellow-alpha': 'var(--color-background-accent-yellow-alpha-default)',
    'accent-yellow-alpha-hovered': 'var(--color-background-accent-yellow-alpha-hovered)',
    'accent-yellow-alpha-pressed': 'var(--color-background-accent-yellow-alpha-pressed)',
    'accent-yellow-bold': 'var(--color-background-accent-yellow-bold-default)',
    'accent-yellow-bold-hovered': 'var(--color-background-accent-yellow-bold-hovered)',
    'accent-yellow-bold-pressed': 'var(--color-background-accent-yellow-bold-pressed)',
    'accent-yellow-bolder': 'var(--color-background-accent-yellow-bolder-default)',
    'accent-yellow-bolder-hovered': 'var(--color-background-accent-yellow-bolder-hovered)',
    'accent-yellow-bolder-pressed': 'var(--color-background-accent-yellow-bolder-pressed)',
    'accent-yellow-subtle': 'var(--color-background-accent-yellow-subtle-default)',
    'accent-yellow-subtle-hovered': 'var(--color-background-accent-yellow-subtle-hovered)',
    'accent-yellow-subtle-pressed': 'var(--color-background-accent-yellow-subtle-pressed)',
    'accent-yellow-subtlest': 'var(--color-background-accent-yellow-subtlest-default)',
    'accent-yellow-subtlest-hovered': 'var(--color-background-accent-yellow-subtlest-hovered)',
    'accent-yellow-subtlest-pressed': 'var(--color-background-accent-yellow-subtlest-pressed)',
    'accent-yellow-white': 'var(--color-background-accent-yellow-white-default)',
    'accent-yellow-white-hovered': 'var(--color-background-accent-yellow-white-hovered)',
    'accent-yellow-white-pressed': 'var(--color-background-accent-yellow-white-pressed)',
    brand: 'var(--color-background-brand-default)',
    'brand-hovered': 'var(--color-background-brand-hovered)',
    'brand-pressed': 'var(--color-background-brand-pressed)',
    'brand-alpha-bold': 'var(--color-background-brand-alpha-bold-default)',
    'brand-alpha-bold-hovered': 'var(--color-background-brand-alpha-bold-hovered)',
    'brand-alpha-bold-pressed': 'var(--color-background-brand-alpha-bold-pressed)',
    'brand-alpha-subtle': 'var(--color-background-brand-alpha-subtle-default)',
    'brand-alpha-subtle-hovered': 'var(--color-background-brand-alpha-subtle-hovered)',
    'brand-alpha-subtle-pressed': 'var(--color-background-brand-alpha-subtle-pressed)',
    'brand-bold': 'var(--color-background-brand-bold-default)',
    'brand-bold-hovered': 'var(--color-background-brand-bold-hovered)',
    'brand-bold-pressed': 'var(--color-background-brand-bold-pressed)',
    'brand-bolder': 'var(--color-background-brand-bolder-default)',
    'brand-bolder-hovered': 'var(--color-background-brand-bolder-hovered)',
    'brand-bolder-pressed': 'var(--color-background-brand-bolder-pressed)',
    'brand-subtle': 'var(--color-background-brand-subtle-default)',
    'brand-subtle-hovered': 'var(--color-background-brand-subtle-hovered)',
    'brand-subtle-pressed': 'var(--color-background-brand-subtle-pressed)',
    'brand-subtlest': 'var(--color-background-brand-subtlest-default)',
    'brand-subtlest-hovered': 'var(--color-background-brand-subtlest-hovered)',
    'brand-subtlest-pressed': 'var(--color-background-brand-subtlest-pressed)',
    'brand-white': 'var(--color-background-brand-white-default)',
    'brand-white-hovered': 'var(--color-background-brand-white-hovered)',
    'brand-white-pressed': 'var(--color-background-brand-white-pressed)',
    danger: 'var(--color-background-danger-default)',
    'danger-hovered': 'var(--color-background-danger-hovered)',
    'danger-pressed': 'var(--color-background-danger-pressed)',
    'danger-alpha-bold': 'var(--color-background-danger-alpha-bold-default)',
    'danger-alpha-bold-hovered': 'var(--color-background-danger-alpha-bold-hovered)',
    'danger-alpha-bold-pressed': 'var(--color-background-danger-alpha-bold-pressed)',
    'danger-alpha-subtle': 'var(--color-background-danger-alpha-subtle-default)',
    'danger-alpha-subtle-hovered': 'var(--color-background-danger-alpha-subtle-hovered)',
    'danger-alpha-subtle-pressed': 'var(--color-background-danger-alpha-subtle-pressed)',
    'danger-bold': 'var(--color-background-danger-bold-default)',
    'danger-bold-hovered': 'var(--color-background-danger-bold-hovered)',
    'danger-bold-pressed': 'var(--color-background-danger-bold-pressed)',
    'danger-bolder': 'var(--color-background-danger-bolder-default)',
    'danger-bolder-hovered': 'var(--color-background-danger-bolder-hovered)',
    'danger-bolder-pressed': 'var(--color-background-danger-bolder-pressed)',
    'danger-subtle': 'var(--color-background-danger-subtle-default)',
    'danger-subtle-hovered': 'var(--color-background-danger-subtle-hovered)',
    'danger-subtle-pressed': 'var(--color-background-danger-subtle-pressed)',
    'danger-subtlest': 'var(--color-background-danger-subtlest-default)',
    'danger-subtlest-hovered': 'var(--color-background-danger-subtlest-hovered)',
    'danger-subtlest-pressed': 'var(--color-background-danger-subtlest-pressed)',
    'danger-white': 'var(--color-background-danger-white-default)',
    'danger-white-hovered': 'var(--color-background-danger-white-hovered)',
    'danger-white-pressed': 'var(--color-background-danger-white-pressed)',
    discovery: 'var(--color-background-discovery-default)',
    'discovery-hovered': 'var(--color-background-discovery-hovered)',
    'discovery-pressed': 'var(--color-background-discovery-pressed)',
    'discovery-alpha-bold': 'var(--color-background-discovery-alpha-bold-default)',
    'discovery-alpha-bold-hovered': 'var(--color-background-discovery-alpha-bold-hovered)',
    'discovery-alpha-bold-pressed': 'var(--color-background-discovery-alpha-bold-pressed)',
    'discovery-alpha-subtle': 'var(--color-background-discovery-alpha-subtle-default)',
    'discovery-alpha-subtle-hovered': 'var(--color-background-discovery-alpha-subtle-hovered)',
    'discovery-alpha-subtle-pressed': 'var(--color-background-discovery-alpha-subtle-pressed)',
    'discovery-bold': 'var(--color-background-discovery-bold-default)',
    'discovery-bold-hovered': 'var(--color-background-discovery-bold-hovered)',
    'discovery-bold-pressed': 'var(--color-background-discovery-bold-pressed)',
    'discovery-bolder': 'var(--color-background-discovery-bolder-default)',
    'discovery-bolder-hovered': 'var(--color-background-discovery-bolder-hovered)',
    'discovery-bolder-pressed': 'var(--color-background-discovery-bolder-pressed)',
    'discovery-subtle': 'var(--color-background-discovery-subtle-default)',
    'discovery-subtle-hovered': 'var(--color-background-discovery-subtle-hovered)',
    'discovery-subtle-pressed': 'var(--color-background-discovery-subtle-pressed)',
    'discovery-subtlest': 'var(--color-background-discovery-subtlest-default)',
    'discovery-subtlest-hovered': 'var(--color-background-discovery-subtlest-hovered)',
    'discovery-subtlest-pressed': 'var(--color-background-discovery-subtlest-pressed)',
    'discovery-white': 'var(--color-background-discovery-white-default)',
    'discovery-white-hovered': 'var(--color-background-discovery-white-hovered)',
    'discovery-white-pressed': 'var(--color-background-discovery-white-pressed)',
    info: 'var(--color-background-info-default)',
    'info-hovered': 'var(--color-background-info-hovered)',
    'info-pressed': 'var(--color-background-info-pressed)',
    'info-alpha-bold': 'var(--color-background-info-alpha-bold-default)',
    'info-alpha-bold-hovered': 'var(--color-background-info-alpha-bold-hovered)',
    'info-alpha-bold-pressed': 'var(--color-background-info-alpha-bold-pressed)',
    'info-alpha-subtle': 'var(--color-background-info-alpha-subtle-default)',
    'info-alpha-subtle-hovered': 'var(--color-background-info-alpha-subtle-hovered)',
    'info-alpha-subtle-pressed': 'var(--color-background-info-alpha-subtle-pressed)',
    'info-bold': 'var(--color-background-info-bold-default)',
    'info-bold-hovered': 'var(--color-background-info-bold-hovered)',
    'info-bold-pressed': 'var(--color-background-info-bold-pressed)',
    'info-bolder': 'var(--color-background-info-bolder-default)',
    'info-bolder-hovered': 'var(--color-background-info-bolder-hovered)',
    'info-bolder-pressed': 'var(--color-background-info-bolder-pressed)',
    'info-subtle': 'var(--color-background-info-subtle-default)',
    'info-subtle-hovered': 'var(--color-background-info-subtle-hovered)',
    'info-subtle-pressed': 'var(--color-background-info-subtle-pressed)',
    'info-subtlest': 'var(--color-background-info-subtlest-default)',
    'info-subtlest-hovered': 'var(--color-background-info-subtlest-hovered)',
    'info-subtlest-pressed': 'var(--color-background-info-subtlest-pressed)',
    'info-white': 'var(--color-background-info-white-default)',
    'info-white-hovered': 'var(--color-background-info-white-hovered)',
    'info-white-pressed': 'var(--color-background-info-white-pressed)',
    input: 'var(--color-background-input-default)',
    'input-disabled': 'var(--color-background-input-disabled)',
    'input-hovered': 'var(--color-background-input-hovered)',
    'input-pressed': 'var(--color-background-input-pressed)',
    'input-danger': 'var(--color-background-input-danger-default)',
    'input-danger-hovered': 'var(--color-background-input-danger-hovered)',
    'input-danger-pressed': 'var(--color-background-input-danger-pressed)',
    'input-selected': 'var(--color-background-input-selected-default)',
    'input-selected-hovered': 'var(--color-background-input-selected-hovered)',
    'input-selected-pressed': 'var(--color-background-input-selected-pressed)',
    neutral: 'var(--color-background-neutral-default)',
    'neutral-hovered': 'var(--color-background-neutral-hovered)',
    'neutral-pressed': 'var(--color-background-neutral-pressed)',
    'neutral-alpha-bold': 'var(--color-background-neutral-alpha-bold-default)',
    'neutral-alpha-bold-hovered': 'var(--color-background-neutral-alpha-bold-hovered)',
    'neutral-alpha-bold-pressed': 'var(--color-background-neutral-alpha-bold-pressed)',
    'neutral-alpha-bold-inverted': 'var(--color-background-neutral-alpha-bold-inverted-default)',
    'neutral-alpha-bold-inverted-hovered': 'var(--color-background-neutral-alpha-bold-inverted-hovered)',
    'neutral-alpha-bold-inverted-pressed': 'var(--color-background-neutral-alpha-bold-inverted-pressed)',
    'neutral-alpha-default': 'var(--color-background-neutral-alpha-default-pressed)',
    'neutral-alpha-default-inverted': 'var(--color-background-neutral-alpha-default-inverted-pressed)',
    'neutral-alpha-subltest': 'var(--color-background-neutral-alpha-subltest-default)',
    'neutral-alpha-subltest-hovered': 'var(--color-background-neutral-alpha-subltest-hovered)',
    'neutral-alpha-subltest-pressed': 'var(--color-background-neutral-alpha-subltest-pressed)',
    'neutral-alpha-subltest-inverted': 'var(--color-background-neutral-alpha-subltest-inverted-default)',
    'neutral-alpha-subltest-inverted-hovered': 'var(--color-background-neutral-alpha-subltest-inverted-hovered)',
    'neutral-alpha-subltest-inverted-pressed': 'var(--color-background-neutral-alpha-subltest-inverted-pressed)',
    'neutral-alpha-subtle': 'var(--color-background-neutral-alpha-subtle-default)',
    'neutral-alpha-subtle-hovered': 'var(--color-background-neutral-alpha-subtle-hovered)',
    'neutral-alpha-subtle-pressed': 'var(--color-background-neutral-alpha-subtle-pressed)',
    'neutral-alpha-subtle-inverted': 'var(--color-background-neutral-alpha-subtle-inverted-default)',
    'neutral-alpha-subtle-inverted-hovered': 'var(--color-background-neutral-alpha-subtle-inverted-hovered)',
    'neutral-alpha-subtle-inverted-pressed': 'var(--color-background-neutral-alpha-subtle-inverted-pressed)',
    'neutral-bold': 'var(--color-background-neutral-bold-default)',
    'neutral-bold-hovered': 'var(--color-background-neutral-bold-hovered)',
    'neutral-bold-pressed': 'var(--color-background-neutral-bold-pressed)',
    'neutral-bolder': 'var(--color-background-neutral-bolder-default)',
    'neutral-bolder-hovered': 'var(--color-background-neutral-bolder-hovered)',
    'neutral-bolder-pressed': 'var(--color-background-neutral-bolder-pressed)',
    'neutral-subtle': 'var(--color-background-neutral-subtle-default)',
    'neutral-subtle-hovered': 'var(--color-background-neutral-subtle-hovered)',
    'neutral-subtle-pressed': 'var(--color-background-neutral-subtle-pressed)',
    'neutral-subtlest': 'var(--color-background-neutral-subtlest-default)',
    'neutral-subtlest-hovered': 'var(--color-background-neutral-subtlest-hovered)',
    'neutral-subtlest-pressed': 'var(--color-background-neutral-subtlest-pressed)',
    'neutral-white': 'var(--color-background-neutral-white-default)',
    'neutral-white-hovered': 'var(--color-background-neutral-white-hovered)',
    'neutral-white-pressed': 'var(--color-background-neutral-white-pressed)',
    selected: 'var(--color-background-selected-default)',
    'selected-hovered': 'var(--color-background-selected-hovered)',
    'selected-pressed': 'var(--color-background-selected-pressed)',
    'selected-alpha': 'var(--color-background-selected-alpha-default)',
    'selected-alpha-hovered': 'var(--color-background-selected-alpha-hovered)',
    'selected-alpha-pressed': 'var(--color-background-selected-alpha-pressed)',
    'selected-bold': 'var(--color-background-selected-bold-default)',
    'selected-bold-hovered': 'var(--color-background-selected-bold-hovered)',
    'selected-bold-pressed': 'var(--color-background-selected-bold-pressed)',
    'selected-bolder': 'var(--color-background-selected-bolder-default)',
    'selected-bolder-hovered': 'var(--color-background-selected-bolder-hovered)',
    'selected-bolder-pressed': 'var(--color-background-selected-bolder-pressed)',
    'selected-subtle': 'var(--color-background-selected-subtle-default)',
    'selected-subtle-hovered': 'var(--color-background-selected-subtle-hovered)',
    'selected-subtle-pressed': 'var(--color-background-selected-subtle-pressed)',
    'selected-white': 'var(--color-background-selected-white-default)',
    'selected-white-hovered': 'var(--color-background-selected-white-hovered)',
    'selected-white-pressed': 'var(--color-background-selected-white-pressed)',
    success: 'var(--color-background-success-default)',
    'success-hovered': 'var(--color-background-success-hovered)',
    'success-pressed': 'var(--color-background-success-pressed)',
    'success-alpha-bold': 'var(--color-background-success-alpha-bold-default)',
    'success-alpha-bold-hovered': 'var(--color-background-success-alpha-bold-hovered)',
    'success-alpha-bold-pressed': 'var(--color-background-success-alpha-bold-pressed)',
    'success-alpha-subtle': 'var(--color-background-success-alpha-subtle-default)',
    'success-alpha-subtle-hovered': 'var(--color-background-success-alpha-subtle-hovered)',
    'success-alpha-subtle-pressed': 'var(--color-background-success-alpha-subtle-pressed)',
    'success-bold': 'var(--color-background-success-bold-default)',
    'success-bold-hovered': 'var(--color-background-success-bold-hovered)',
    'success-bold-pressed': 'var(--color-background-success-bold-pressed)',
    'success-bolder': 'var(--color-background-success-bolder-default)',
    'success-bolder-hovered': 'var(--color-background-success-bolder-hovered)',
    'success-bolder-pressed': 'var(--color-background-success-bolder-pressed)',
    'success-subtle': 'var(--color-background-success-subtle-default)',
    'success-subtle-hovered': 'var(--color-background-success-subtle-hovered)',
    'success-subtle-pressed': 'var(--color-background-success-subtle-pressed)',
    'success-subtlest': 'var(--color-background-success-subtlest-default)',
    'success-subtlest-hovered': 'var(--color-background-success-subtlest-hovered)',
    'success-subtlest-pressed': 'var(--color-background-success-subtlest-pressed)',
    'success-white': 'var(--color-background-success-white-default)',
    'success-white-hovered': 'var(--color-background-success-white-hovered)',
    'success-white-pressed': 'var(--color-background-success-white-pressed)',
    warning: 'var(--color-background-warning-default)',
    'warning-hovered': 'var(--color-background-warning-hovered)',
    'warning-pressed': 'var(--color-background-warning-pressed)',
    'warning-alpha-bold': 'var(--color-background-warning-alpha-bold-default)',
    'warning-alpha-bold-hovered': 'var(--color-background-warning-alpha-bold-hovered)',
    'warning-alpha-bold-pressed': 'var(--color-background-warning-alpha-bold-pressed)',
    'warning-alpha-subtle': 'var(--color-background-warning-alpha-subtle-default)',
    'warning-alpha-subtle-hovered': 'var(--color-background-warning-alpha-subtle-hovered)',
    'warning-alpha-subtle-pressed': 'var(--color-background-warning-alpha-subtle-pressed)',
    'warning-bold': 'var(--color-background-warning-bold-default)',
    'warning-bold-hovered': 'var(--color-background-warning-bold-hovered)',
    'warning-bold-pressed': 'var(--color-background-warning-bold-pressed)',
    'warning-bolder': 'var(--color-background-warning-bolder-default)',
    'warning-bolder-hovered': 'var(--color-background-warning-bolder-hovered)',
    'warning-bolder-pressed': 'var(--color-background-warning-bolder-pressed)',
    'warning-subtle': 'var(--color-background-warning-subtle-default)',
    'warning-subtle-hovered': 'var(--color-background-warning-subtle-hovered)',
    'warning-subtle-pressed': 'var(--color-background-warning-subtle-pressed)',
    'warning-subtlest': 'var(--color-background-warning-subtlest-default)',
    'warning-subtlest-hovered': 'var(--color-background-warning-subtlest-hovered)',
    'warning-subtlest-pressed': 'var(--color-background-warning-subtlest-pressed)',
    'warning-white': 'var(--color-background-warning-white-default)',
    'warning-white-hovered': 'var(--color-background-warning-white-hovered)',
    'warning-white-pressed': 'var(--color-background-warning-white-pressed)',
    overlay: 'var(--color-overlay-default)',
    'overlay-subtle': 'var(--color-overlay-subtle)',
    'overlay-inverse': 'var(--color-overlay-inverse-default)',
    'overlay-inverse-subtle': 'var(--color-overlay-inverse-subtle)',
    skeleton: 'var(--color-skeleton-default)',
    'skeleton-subtle': 'var(--color-skeleton-subtle)',
    surface: 'var(--color-surface-default)',
    'surface-hovered': 'var(--color-surface-hovered)',
    'surface-pressed': 'var(--color-surface-pressed)',
    'surface-inverse': 'var(--color-surface-inverse-default)',
    'surface-inverse-hovered': 'var(--color-surface-inverse-hovered)',
    'surface-inverse-pressed': 'var(--color-surface-inverse-pressed)',
    'surface-navigation': 'var(--color-surface-navigation-default)',
    'surface-navigation-hovered': 'var(--color-surface-navigation-hovered)',
    'surface-navigation-pressed': 'var(--color-surface-navigation-pressed)',
    'surface-overlay': 'var(--color-surface-overlay-default)',
    'surface-overlay-hovered': 'var(--color-surface-overlay-hovered)',
    'surface-overlay-pressed': 'var(--color-surface-overlay-pressed)',
    'surface-raised': 'var(--color-surface-raised-default)',
    'surface-raised-hovered': 'var(--color-surface-raised-hovered)',
    'surface-raised-pressed': 'var(--color-surface-raised-pressed)',
    'surface-sunken': 'var(--color-surface-sunken-default)',
  },
};

export type Variables = typeof variables;
export { variables };
